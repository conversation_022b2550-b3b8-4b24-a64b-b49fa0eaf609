# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: live/business/deny_config.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1flive/business/deny_config.proto\x12\x1fVolcengine.Live.Models.Business\"\xa8\x01\n\x10\x44\x65nyConfigDetail\x12\x0f\n\x07ProType\x18\x01 \x03(\t\x12\x0f\n\x07\x46mtType\x18\x02 \x03(\t\x12\x11\n\tContinent\x18\x03 \x01(\t\x12\x0f\n\x07\x43ountry\x18\x04 \x01(\t\x12\x0e\n\x06Region\x18\x05 \x01(\t\x12\x0c\n\x04\x43ity\x18\x06 \x01(\t\x12\x0b\n\x03ISP\x18\x07 \x01(\t\x12\x10\n\x08\x44\x65nyList\x18\x08 \x03(\t\x12\x11\n\tAllowList\x18\t \x03(\t\"b\n\x18\x44\x65scribeDenyConfigResult\x12\x46\n\x08\x44\x65nyList\x18\x01 \x03(\x0b\x32\x34.Volcengine.Live.Models.Business.VhostWithDenyConfig\"\x8e\x01\n\x13VhostWithDenyConfig\x12\r\n\x05Vhost\x18\x01 \x01(\t\x12\x0e\n\x06\x44omain\x18\x02 \x01(\t\x12\x0b\n\x03\x41pp\x18\x03 \x01(\t\x12K\n\x10\x44\x65nyConfigDetail\x18\x04 \x03(\x0b\x32\x31.Volcengine.Live.Models.Business.DenyConfigDetailB\xd2\x01\n*com.volcengine.service.live.model.businessB\nDenyConfigP\x01ZBgithub.com/volcengine/volc-sdk-golang/service/live/models/business\xa0\x01\x01\xd8\x01\x01\xc2\x02\x00\xca\x02!Volc\\Service\\Live\\Models\\Business\xe2\x02$Volc\\Service\\Live\\Models\\GPBMetadatab\x06proto3')



_DENYCONFIGDETAIL = DESCRIPTOR.message_types_by_name['DenyConfigDetail']
_DESCRIBEDENYCONFIGRESULT = DESCRIPTOR.message_types_by_name['DescribeDenyConfigResult']
_VHOSTWITHDENYCONFIG = DESCRIPTOR.message_types_by_name['VhostWithDenyConfig']
DenyConfigDetail = _reflection.GeneratedProtocolMessageType('DenyConfigDetail', (_message.Message,), {
  'DESCRIPTOR' : _DENYCONFIGDETAIL,
  '__module__' : 'live.business.deny_config_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Business.DenyConfigDetail)
  })
_sym_db.RegisterMessage(DenyConfigDetail)

DescribeDenyConfigResult = _reflection.GeneratedProtocolMessageType('DescribeDenyConfigResult', (_message.Message,), {
  'DESCRIPTOR' : _DESCRIBEDENYCONFIGRESULT,
  '__module__' : 'live.business.deny_config_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Business.DescribeDenyConfigResult)
  })
_sym_db.RegisterMessage(DescribeDenyConfigResult)

VhostWithDenyConfig = _reflection.GeneratedProtocolMessageType('VhostWithDenyConfig', (_message.Message,), {
  'DESCRIPTOR' : _VHOSTWITHDENYCONFIG,
  '__module__' : 'live.business.deny_config_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Business.VhostWithDenyConfig)
  })
_sym_db.RegisterMessage(VhostWithDenyConfig)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n*com.volcengine.service.live.model.businessB\nDenyConfigP\001ZBgithub.com/volcengine/volc-sdk-golang/service/live/models/business\240\001\001\330\001\001\302\002\000\312\002!Volc\\Service\\Live\\Models\\Business\342\002$Volc\\Service\\Live\\Models\\GPBMetadata'
  _DENYCONFIGDETAIL._serialized_start=69
  _DENYCONFIGDETAIL._serialized_end=237
  _DESCRIBEDENYCONFIGRESULT._serialized_start=239
  _DESCRIBEDENYCONFIGRESULT._serialized_end=337
  _VHOSTWITHDENYCONFIG._serialized_start=340
  _VHOSTWITHDENYCONFIG._serialized_end=482
# @@protoc_insertion_point(module_scope)
