# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: live/response/response_live.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from volcengine.base.models.base import base_pb2 as base_dot_base__pb2
from volcengine.live.models.business import domain_pb2 as live_dot_business_dot_domain__pb2
from volcengine.live.models.business import VQScore_pb2 as live_dot_business_dot_VQScore__pb2
from volcengine.live.models.business import addr_pb2 as live_dot_business_dot_addr__pb2
from volcengine.live.models.business import pull_to_push_pb2 as live_dot_business_dot_pull__to__push__pb2
from volcengine.live.models.business import deny_config_pb2 as live_dot_business_dot_deny__config__pb2
from volcengine.live.models.business import relay_source_pb2 as live_dot_business_dot_relay__source__pb2
from volcengine.live.models.business import stream_manage_pb2 as live_dot_business_dot_stream__manage__pb2
from volcengine.live.models.business import snapshot_manage_pb2 as live_dot_business_dot_snapshot__manage__pb2
from volcengine.live.models.business import record_manage_pb2 as live_dot_business_dot_record__manage__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n!live/response/response_live.proto\x12\x1fVolcengine.Live.Models.Response\x1a\x0f\x62\x61se/base.proto\x1a\x1alive/business/domain.proto\x1a\x1blive/business/VQScore.proto\x1a\x18live/business/addr.proto\x1a live/business/pull_to_push.proto\x1a\x1flive/business/deny_config.proto\x1a live/business/relay_source.proto\x1a!live/business/stream_manage.proto\x1a#live/business/snapshot_manage.proto\x1a!live/business/record_manage.proto\"_\n\x14\x43reateDomainResponse\x12G\n\x10ResponseMetadata\x18\x01 \x01(\x0b\x32-.Volcengine.Base.Models.Base.ResponseMetadata\"\xa4\x01\n\x18ListDomainDetailResponse\x12G\n\x10ResponseMetadata\x18\x01 \x01(\x0b\x32-.Volcengine.Base.Models.Base.ResponseMetadata\x12?\n\x06Result\x18\x02 \x01(\x0b\x32/.Volcengine.Live.Models.Business.DomainListInfo\"`\n\x15\x44isableDomainResponse\x12G\n\x10ResponseMetadata\x18\x01 \x01(\x0b\x32-.Volcengine.Base.Models.Base.ResponseMetadata\"_\n\x14\x45nableDomainResponse\x12G\n\x10ResponseMetadata\x18\x01 \x01(\x0b\x32-.Volcengine.Base.Models.Base.ResponseMetadata\"_\n\x14\x44\x65leteDomainResponse\x12G\n\x10ResponseMetadata\x18\x01 \x01(\x0b\x32-.Volcengine.Base.Models.Base.ResponseMetadata\"\xa2\x01\n\x16\x44\x65scribeDomainResponse\x12G\n\x10ResponseMetadata\x18\x01 \x01(\x0b\x32-.Volcengine.Base.Models.Base.ResponseMetadata\x12?\n\x06Result\x18\x02 \x01(\x0b\x32/.Volcengine.Live.Models.Business.DomainListInfo\"l\n!ManagerPullPushDomainBindResponse\x12G\n\x10ResponseMetadata\x18\x01 \x01(\x0b\x32-.Volcengine.Base.Models.Base.ResponseMetadata\"\xb0\x01\n$DescribeLiveStreamInfoByPageResponse\x12G\n\x10ResponseMetadata\x18\x01 \x01(\x0b\x32-.Volcengine.Base.Models.Base.ResponseMetadata\x12?\n\x06Result\x18\x02 \x01(\x0b\x32/.Volcengine.Live.Models.Business.LiveStreamInfo\"\xb4\x01\n&DescribeClosedStreamInfoByPageResponse\x12G\n\x10ResponseMetadata\x18\x01 \x01(\x0b\x32-.Volcengine.Base.Models.Base.ResponseMetadata\x12\x41\n\x06Result\x18\x02 \x01(\x0b\x32\x31.Volcengine.Live.Models.Business.ClosedStreamInfo\"\xac\x01\n\x1f\x44\x65scribeLiveStreamStateResponse\x12G\n\x10ResponseMetadata\x18\x01 \x01(\x0b\x32-.Volcengine.Base.Models.Base.ResponseMetadata\x12@\n\x06Result\x18\x02 \x01(\x0b\x32\x30.Volcengine.Live.Models.Business.StreamStateInfo\"]\n\x12KillStreamResponse\x12G\n\x10ResponseMetadata\x18\x01 \x01(\x0b\x32-.Volcengine.Base.Models.Base.ResponseMetadata\"_\n\x14\x46orbidStreamResponse\x12G\n\x10ResponseMetadata\x18\x01 \x01(\x0b\x32-.Volcengine.Base.Models.Base.ResponseMetadata\"\xba\x01\n)DescribeForbiddenStreamInfoByPageResponse\x12G\n\x10ResponseMetadata\x18\x01 \x01(\x0b\x32-.Volcengine.Base.Models.Base.ResponseMetadata\x12\x44\n\x06Result\x18\x02 \x01(\x0b\x32\x34.Volcengine.Live.Models.Business.ForbiddenStreamInfo\"_\n\x14ResumeStreamResponse\x12G\n\x10ResponseMetadata\x18\x01 \x01(\x0b\x32-.Volcengine.Base.Models.Base.ResponseMetadata\"\xb6\x01\n\"DescribeCDNSnapshotHistoryResponse\x12G\n\x10ResponseMetadata\x18\x01 \x01(\x0b\x32-.Volcengine.Base.Models.Base.ResponseMetadata\x12G\n\x06Result\x18\x02 \x01(\x0b\x32\x37.Volcengine.Live.Models.Business.CDNSnapshotHistoryInfo\"\xb4\x01\n%DescribeRecordTaskFileHistoryResponse\x12G\n\x10ResponseMetadata\x18\x01 \x01(\x0b\x32-.Volcengine.Base.Models.Base.ResponseMetadata\x12\x42\n\x06Result\x18\x02 \x01(\x0b\x32\x32.Volcengine.Live.Models.Business.RecordHistoryInfo\"d\n\x19UpdateRelaySourceResponse\x12G\n\x10ResponseMetadata\x18\x01 \x01(\x0b\x32-.Volcengine.Base.Models.Base.ResponseMetadata\"d\n\x19\x44\x65leteRelaySourceResponse\x12G\n\x10ResponseMetadata\x18\x01 \x01(\x0b\x32-.Volcengine.Base.Models.Base.ResponseMetadata\"\xae\x01\n\x1b\x44\x65scribeRelaySourceResponse\x12G\n\x10ResponseMetadata\x18\x01 \x01(\x0b\x32-.Volcengine.Base.Models.Base.ResponseMetadata\x12\x46\n\x06Result\x18\x02 \x01(\x0b\x32\x36.Volcengine.Live.Models.Business.RelaySourceConfigList\"\xa0\x01\n\x19\x43reateVQScoreTaskResponse\x12G\n\x10ResponseMetadata\x18\x01 \x01(\x0b\x32-.Volcengine.Base.Models.Base.ResponseMetadata\x12:\n\x06Result\x18\x02 \x01(\x0b\x32*.Volcengine.Live.Models.Business.VQScoreID\"\xa4\x01\n\x1b\x44\x65scribeVQScoreTaskResponse\x12G\n\x10ResponseMetadata\x18\x01 \x01(\x0b\x32-.Volcengine.Base.Models.Base.ResponseMetadata\x12<\n\x06Result\x18\x02 \x01(\x0b\x32,.Volcengine.Live.Models.Business.VQScoreInfo\"\xa8\x01\n\x17ListVQScoreTaskResponse\x12G\n\x10ResponseMetadata\x18\x01 \x01(\x0b\x32-.Volcengine.Base.Models.Base.ResponseMetadata\x12\x44\n\x06Result\x18\x02 \x01(\x0b\x32\x34.Volcengine.Live.Models.Business.VQScoreTaskListInfo\"\xaa\x01\n\x17GeneratePlayURLResponse\x12G\n\x10ResponseMetadata\x18\x01 \x01(\x0b\x32-.Volcengine.Base.Models.Base.ResponseMetadata\x12\x46\n\x06Result\x18\x02 \x01(\x0b\x32\x36.Volcengine.Live.Models.Business.GeneratePlayURLResult\"\xaa\x01\n\x17GeneratePushURLResponse\x12G\n\x10ResponseMetadata\x18\x01 \x01(\x0b\x32-.Volcengine.Base.Models.Base.ResponseMetadata\x12\x46\n\x06Result\x18\x02 \x01(\x0b\x32\x36.Volcengine.Live.Models.Business.GeneratePushURLResult\"\xb4\x01\n\x1c\x43reatePullToPushTaskResponse\x12G\n\x10ResponseMetadata\x18\x01 \x01(\x0b\x32-.Volcengine.Base.Models.Base.ResponseMetadata\x12K\n\x06Result\x18\x02 \x01(\x0b\x32;.Volcengine.Live.Models.Business.CreatePullToPushTaskResult\"\xb0\x01\n\x1aListPullToPushTaskResponse\x12G\n\x10ResponseMetadata\x18\x01 \x01(\x0b\x32-.Volcengine.Base.Models.Base.ResponseMetadata\x12I\n\x06Result\x18\x02 \x01(\x0b\x32\x39.Volcengine.Live.Models.Business.ListPullToPushTaskResult\"g\n\x1cUpdatePullToPushTaskResponse\x12G\n\x10ResponseMetadata\x18\x01 \x01(\x0b\x32-.Volcengine.Base.Models.Base.ResponseMetadata\"h\n\x1dRestartPullToPushTaskResponse\x12G\n\x10ResponseMetadata\x18\x01 \x01(\x0b\x32-.Volcengine.Base.Models.Base.ResponseMetadata\"e\n\x1aStopPullToPushTaskResponse\x12G\n\x10ResponseMetadata\x18\x01 \x01(\x0b\x32-.Volcengine.Base.Models.Base.ResponseMetadata\"g\n\x1c\x44\x65letePullToPushTaskResponse\x12G\n\x10ResponseMetadata\x18\x01 \x01(\x0b\x32-.Volcengine.Base.Models.Base.ResponseMetadata\"c\n\x18UpdateDenyConfigResponse\x12G\n\x10ResponseMetadata\x18\x01 \x01(\x0b\x32-.Volcengine.Base.Models.Base.ResponseMetadata\"\xb0\x01\n\x1a\x44\x65scribeDenyConfigResponse\x12G\n\x10ResponseMetadata\x18\x01 \x01(\x0b\x32-.Volcengine.Base.Models.Base.ResponseMetadata\x12I\n\x06Result\x18\x02 \x01(\x0b\x32\x39.Volcengine.Live.Models.Business.DescribeDenyConfigResult\"c\n\x18\x44\x65leteDenyConfigResponse\x12G\n\x10ResponseMetadata\x18\x01 \x01(\x0b\x32-.Volcengine.Base.Models.Base.ResponseMetadataB\xd1\x01\n*com.volcengine.service.live.model.responseB\x0cLiveResponseP\x01ZBgithub.com/volcengine/volc-sdk-golang/service/live/models/response\xa0\x01\x01\xd8\x01\x01\xca\x02!Volc\\Service\\Live\\Models\\Response\xe2\x02$Volc\\Service\\Live\\Models\\GPBMetadatab\x06proto3')



_CREATEDOMAINRESPONSE = DESCRIPTOR.message_types_by_name['CreateDomainResponse']
_LISTDOMAINDETAILRESPONSE = DESCRIPTOR.message_types_by_name['ListDomainDetailResponse']
_DISABLEDOMAINRESPONSE = DESCRIPTOR.message_types_by_name['DisableDomainResponse']
_ENABLEDOMAINRESPONSE = DESCRIPTOR.message_types_by_name['EnableDomainResponse']
_DELETEDOMAINRESPONSE = DESCRIPTOR.message_types_by_name['DeleteDomainResponse']
_DESCRIBEDOMAINRESPONSE = DESCRIPTOR.message_types_by_name['DescribeDomainResponse']
_MANAGERPULLPUSHDOMAINBINDRESPONSE = DESCRIPTOR.message_types_by_name['ManagerPullPushDomainBindResponse']
_DESCRIBELIVESTREAMINFOBYPAGERESPONSE = DESCRIPTOR.message_types_by_name['DescribeLiveStreamInfoByPageResponse']
_DESCRIBECLOSEDSTREAMINFOBYPAGERESPONSE = DESCRIPTOR.message_types_by_name['DescribeClosedStreamInfoByPageResponse']
_DESCRIBELIVESTREAMSTATERESPONSE = DESCRIPTOR.message_types_by_name['DescribeLiveStreamStateResponse']
_KILLSTREAMRESPONSE = DESCRIPTOR.message_types_by_name['KillStreamResponse']
_FORBIDSTREAMRESPONSE = DESCRIPTOR.message_types_by_name['ForbidStreamResponse']
_DESCRIBEFORBIDDENSTREAMINFOBYPAGERESPONSE = DESCRIPTOR.message_types_by_name['DescribeForbiddenStreamInfoByPageResponse']
_RESUMESTREAMRESPONSE = DESCRIPTOR.message_types_by_name['ResumeStreamResponse']
_DESCRIBECDNSNAPSHOTHISTORYRESPONSE = DESCRIPTOR.message_types_by_name['DescribeCDNSnapshotHistoryResponse']
_DESCRIBERECORDTASKFILEHISTORYRESPONSE = DESCRIPTOR.message_types_by_name['DescribeRecordTaskFileHistoryResponse']
_UPDATERELAYSOURCERESPONSE = DESCRIPTOR.message_types_by_name['UpdateRelaySourceResponse']
_DELETERELAYSOURCERESPONSE = DESCRIPTOR.message_types_by_name['DeleteRelaySourceResponse']
_DESCRIBERELAYSOURCERESPONSE = DESCRIPTOR.message_types_by_name['DescribeRelaySourceResponse']
_CREATEVQSCORETASKRESPONSE = DESCRIPTOR.message_types_by_name['CreateVQScoreTaskResponse']
_DESCRIBEVQSCORETASKRESPONSE = DESCRIPTOR.message_types_by_name['DescribeVQScoreTaskResponse']
_LISTVQSCORETASKRESPONSE = DESCRIPTOR.message_types_by_name['ListVQScoreTaskResponse']
_GENERATEPLAYURLRESPONSE = DESCRIPTOR.message_types_by_name['GeneratePlayURLResponse']
_GENERATEPUSHURLRESPONSE = DESCRIPTOR.message_types_by_name['GeneratePushURLResponse']
_CREATEPULLTOPUSHTASKRESPONSE = DESCRIPTOR.message_types_by_name['CreatePullToPushTaskResponse']
_LISTPULLTOPUSHTASKRESPONSE = DESCRIPTOR.message_types_by_name['ListPullToPushTaskResponse']
_UPDATEPULLTOPUSHTASKRESPONSE = DESCRIPTOR.message_types_by_name['UpdatePullToPushTaskResponse']
_RESTARTPULLTOPUSHTASKRESPONSE = DESCRIPTOR.message_types_by_name['RestartPullToPushTaskResponse']
_STOPPULLTOPUSHTASKRESPONSE = DESCRIPTOR.message_types_by_name['StopPullToPushTaskResponse']
_DELETEPULLTOPUSHTASKRESPONSE = DESCRIPTOR.message_types_by_name['DeletePullToPushTaskResponse']
_UPDATEDENYCONFIGRESPONSE = DESCRIPTOR.message_types_by_name['UpdateDenyConfigResponse']
_DESCRIBEDENYCONFIGRESPONSE = DESCRIPTOR.message_types_by_name['DescribeDenyConfigResponse']
_DELETEDENYCONFIGRESPONSE = DESCRIPTOR.message_types_by_name['DeleteDenyConfigResponse']
CreateDomainResponse = _reflection.GeneratedProtocolMessageType('CreateDomainResponse', (_message.Message,), {
  'DESCRIPTOR' : _CREATEDOMAINRESPONSE,
  '__module__' : 'live.response.response_live_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Response.CreateDomainResponse)
  })
_sym_db.RegisterMessage(CreateDomainResponse)

ListDomainDetailResponse = _reflection.GeneratedProtocolMessageType('ListDomainDetailResponse', (_message.Message,), {
  'DESCRIPTOR' : _LISTDOMAINDETAILRESPONSE,
  '__module__' : 'live.response.response_live_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Response.ListDomainDetailResponse)
  })
_sym_db.RegisterMessage(ListDomainDetailResponse)

DisableDomainResponse = _reflection.GeneratedProtocolMessageType('DisableDomainResponse', (_message.Message,), {
  'DESCRIPTOR' : _DISABLEDOMAINRESPONSE,
  '__module__' : 'live.response.response_live_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Response.DisableDomainResponse)
  })
_sym_db.RegisterMessage(DisableDomainResponse)

EnableDomainResponse = _reflection.GeneratedProtocolMessageType('EnableDomainResponse', (_message.Message,), {
  'DESCRIPTOR' : _ENABLEDOMAINRESPONSE,
  '__module__' : 'live.response.response_live_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Response.EnableDomainResponse)
  })
_sym_db.RegisterMessage(EnableDomainResponse)

DeleteDomainResponse = _reflection.GeneratedProtocolMessageType('DeleteDomainResponse', (_message.Message,), {
  'DESCRIPTOR' : _DELETEDOMAINRESPONSE,
  '__module__' : 'live.response.response_live_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Response.DeleteDomainResponse)
  })
_sym_db.RegisterMessage(DeleteDomainResponse)

DescribeDomainResponse = _reflection.GeneratedProtocolMessageType('DescribeDomainResponse', (_message.Message,), {
  'DESCRIPTOR' : _DESCRIBEDOMAINRESPONSE,
  '__module__' : 'live.response.response_live_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Response.DescribeDomainResponse)
  })
_sym_db.RegisterMessage(DescribeDomainResponse)

ManagerPullPushDomainBindResponse = _reflection.GeneratedProtocolMessageType('ManagerPullPushDomainBindResponse', (_message.Message,), {
  'DESCRIPTOR' : _MANAGERPULLPUSHDOMAINBINDRESPONSE,
  '__module__' : 'live.response.response_live_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Response.ManagerPullPushDomainBindResponse)
  })
_sym_db.RegisterMessage(ManagerPullPushDomainBindResponse)

DescribeLiveStreamInfoByPageResponse = _reflection.GeneratedProtocolMessageType('DescribeLiveStreamInfoByPageResponse', (_message.Message,), {
  'DESCRIPTOR' : _DESCRIBELIVESTREAMINFOBYPAGERESPONSE,
  '__module__' : 'live.response.response_live_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Response.DescribeLiveStreamInfoByPageResponse)
  })
_sym_db.RegisterMessage(DescribeLiveStreamInfoByPageResponse)

DescribeClosedStreamInfoByPageResponse = _reflection.GeneratedProtocolMessageType('DescribeClosedStreamInfoByPageResponse', (_message.Message,), {
  'DESCRIPTOR' : _DESCRIBECLOSEDSTREAMINFOBYPAGERESPONSE,
  '__module__' : 'live.response.response_live_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Response.DescribeClosedStreamInfoByPageResponse)
  })
_sym_db.RegisterMessage(DescribeClosedStreamInfoByPageResponse)

DescribeLiveStreamStateResponse = _reflection.GeneratedProtocolMessageType('DescribeLiveStreamStateResponse', (_message.Message,), {
  'DESCRIPTOR' : _DESCRIBELIVESTREAMSTATERESPONSE,
  '__module__' : 'live.response.response_live_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Response.DescribeLiveStreamStateResponse)
  })
_sym_db.RegisterMessage(DescribeLiveStreamStateResponse)

KillStreamResponse = _reflection.GeneratedProtocolMessageType('KillStreamResponse', (_message.Message,), {
  'DESCRIPTOR' : _KILLSTREAMRESPONSE,
  '__module__' : 'live.response.response_live_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Response.KillStreamResponse)
  })
_sym_db.RegisterMessage(KillStreamResponse)

ForbidStreamResponse = _reflection.GeneratedProtocolMessageType('ForbidStreamResponse', (_message.Message,), {
  'DESCRIPTOR' : _FORBIDSTREAMRESPONSE,
  '__module__' : 'live.response.response_live_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Response.ForbidStreamResponse)
  })
_sym_db.RegisterMessage(ForbidStreamResponse)

DescribeForbiddenStreamInfoByPageResponse = _reflection.GeneratedProtocolMessageType('DescribeForbiddenStreamInfoByPageResponse', (_message.Message,), {
  'DESCRIPTOR' : _DESCRIBEFORBIDDENSTREAMINFOBYPAGERESPONSE,
  '__module__' : 'live.response.response_live_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Response.DescribeForbiddenStreamInfoByPageResponse)
  })
_sym_db.RegisterMessage(DescribeForbiddenStreamInfoByPageResponse)

ResumeStreamResponse = _reflection.GeneratedProtocolMessageType('ResumeStreamResponse', (_message.Message,), {
  'DESCRIPTOR' : _RESUMESTREAMRESPONSE,
  '__module__' : 'live.response.response_live_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Response.ResumeStreamResponse)
  })
_sym_db.RegisterMessage(ResumeStreamResponse)

DescribeCDNSnapshotHistoryResponse = _reflection.GeneratedProtocolMessageType('DescribeCDNSnapshotHistoryResponse', (_message.Message,), {
  'DESCRIPTOR' : _DESCRIBECDNSNAPSHOTHISTORYRESPONSE,
  '__module__' : 'live.response.response_live_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Response.DescribeCDNSnapshotHistoryResponse)
  })
_sym_db.RegisterMessage(DescribeCDNSnapshotHistoryResponse)

DescribeRecordTaskFileHistoryResponse = _reflection.GeneratedProtocolMessageType('DescribeRecordTaskFileHistoryResponse', (_message.Message,), {
  'DESCRIPTOR' : _DESCRIBERECORDTASKFILEHISTORYRESPONSE,
  '__module__' : 'live.response.response_live_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Response.DescribeRecordTaskFileHistoryResponse)
  })
_sym_db.RegisterMessage(DescribeRecordTaskFileHistoryResponse)

UpdateRelaySourceResponse = _reflection.GeneratedProtocolMessageType('UpdateRelaySourceResponse', (_message.Message,), {
  'DESCRIPTOR' : _UPDATERELAYSOURCERESPONSE,
  '__module__' : 'live.response.response_live_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Response.UpdateRelaySourceResponse)
  })
_sym_db.RegisterMessage(UpdateRelaySourceResponse)

DeleteRelaySourceResponse = _reflection.GeneratedProtocolMessageType('DeleteRelaySourceResponse', (_message.Message,), {
  'DESCRIPTOR' : _DELETERELAYSOURCERESPONSE,
  '__module__' : 'live.response.response_live_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Response.DeleteRelaySourceResponse)
  })
_sym_db.RegisterMessage(DeleteRelaySourceResponse)

DescribeRelaySourceResponse = _reflection.GeneratedProtocolMessageType('DescribeRelaySourceResponse', (_message.Message,), {
  'DESCRIPTOR' : _DESCRIBERELAYSOURCERESPONSE,
  '__module__' : 'live.response.response_live_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Response.DescribeRelaySourceResponse)
  })
_sym_db.RegisterMessage(DescribeRelaySourceResponse)

CreateVQScoreTaskResponse = _reflection.GeneratedProtocolMessageType('CreateVQScoreTaskResponse', (_message.Message,), {
  'DESCRIPTOR' : _CREATEVQSCORETASKRESPONSE,
  '__module__' : 'live.response.response_live_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Response.CreateVQScoreTaskResponse)
  })
_sym_db.RegisterMessage(CreateVQScoreTaskResponse)

DescribeVQScoreTaskResponse = _reflection.GeneratedProtocolMessageType('DescribeVQScoreTaskResponse', (_message.Message,), {
  'DESCRIPTOR' : _DESCRIBEVQSCORETASKRESPONSE,
  '__module__' : 'live.response.response_live_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Response.DescribeVQScoreTaskResponse)
  })
_sym_db.RegisterMessage(DescribeVQScoreTaskResponse)

ListVQScoreTaskResponse = _reflection.GeneratedProtocolMessageType('ListVQScoreTaskResponse', (_message.Message,), {
  'DESCRIPTOR' : _LISTVQSCORETASKRESPONSE,
  '__module__' : 'live.response.response_live_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Response.ListVQScoreTaskResponse)
  })
_sym_db.RegisterMessage(ListVQScoreTaskResponse)

GeneratePlayURLResponse = _reflection.GeneratedProtocolMessageType('GeneratePlayURLResponse', (_message.Message,), {
  'DESCRIPTOR' : _GENERATEPLAYURLRESPONSE,
  '__module__' : 'live.response.response_live_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Response.GeneratePlayURLResponse)
  })
_sym_db.RegisterMessage(GeneratePlayURLResponse)

GeneratePushURLResponse = _reflection.GeneratedProtocolMessageType('GeneratePushURLResponse', (_message.Message,), {
  'DESCRIPTOR' : _GENERATEPUSHURLRESPONSE,
  '__module__' : 'live.response.response_live_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Response.GeneratePushURLResponse)
  })
_sym_db.RegisterMessage(GeneratePushURLResponse)

CreatePullToPushTaskResponse = _reflection.GeneratedProtocolMessageType('CreatePullToPushTaskResponse', (_message.Message,), {
  'DESCRIPTOR' : _CREATEPULLTOPUSHTASKRESPONSE,
  '__module__' : 'live.response.response_live_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Response.CreatePullToPushTaskResponse)
  })
_sym_db.RegisterMessage(CreatePullToPushTaskResponse)

ListPullToPushTaskResponse = _reflection.GeneratedProtocolMessageType('ListPullToPushTaskResponse', (_message.Message,), {
  'DESCRIPTOR' : _LISTPULLTOPUSHTASKRESPONSE,
  '__module__' : 'live.response.response_live_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Response.ListPullToPushTaskResponse)
  })
_sym_db.RegisterMessage(ListPullToPushTaskResponse)

UpdatePullToPushTaskResponse = _reflection.GeneratedProtocolMessageType('UpdatePullToPushTaskResponse', (_message.Message,), {
  'DESCRIPTOR' : _UPDATEPULLTOPUSHTASKRESPONSE,
  '__module__' : 'live.response.response_live_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Response.UpdatePullToPushTaskResponse)
  })
_sym_db.RegisterMessage(UpdatePullToPushTaskResponse)

RestartPullToPushTaskResponse = _reflection.GeneratedProtocolMessageType('RestartPullToPushTaskResponse', (_message.Message,), {
  'DESCRIPTOR' : _RESTARTPULLTOPUSHTASKRESPONSE,
  '__module__' : 'live.response.response_live_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Response.RestartPullToPushTaskResponse)
  })
_sym_db.RegisterMessage(RestartPullToPushTaskResponse)

StopPullToPushTaskResponse = _reflection.GeneratedProtocolMessageType('StopPullToPushTaskResponse', (_message.Message,), {
  'DESCRIPTOR' : _STOPPULLTOPUSHTASKRESPONSE,
  '__module__' : 'live.response.response_live_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Response.StopPullToPushTaskResponse)
  })
_sym_db.RegisterMessage(StopPullToPushTaskResponse)

DeletePullToPushTaskResponse = _reflection.GeneratedProtocolMessageType('DeletePullToPushTaskResponse', (_message.Message,), {
  'DESCRIPTOR' : _DELETEPULLTOPUSHTASKRESPONSE,
  '__module__' : 'live.response.response_live_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Response.DeletePullToPushTaskResponse)
  })
_sym_db.RegisterMessage(DeletePullToPushTaskResponse)

UpdateDenyConfigResponse = _reflection.GeneratedProtocolMessageType('UpdateDenyConfigResponse', (_message.Message,), {
  'DESCRIPTOR' : _UPDATEDENYCONFIGRESPONSE,
  '__module__' : 'live.response.response_live_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Response.UpdateDenyConfigResponse)
  })
_sym_db.RegisterMessage(UpdateDenyConfigResponse)

DescribeDenyConfigResponse = _reflection.GeneratedProtocolMessageType('DescribeDenyConfigResponse', (_message.Message,), {
  'DESCRIPTOR' : _DESCRIBEDENYCONFIGRESPONSE,
  '__module__' : 'live.response.response_live_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Response.DescribeDenyConfigResponse)
  })
_sym_db.RegisterMessage(DescribeDenyConfigResponse)

DeleteDenyConfigResponse = _reflection.GeneratedProtocolMessageType('DeleteDenyConfigResponse', (_message.Message,), {
  'DESCRIPTOR' : _DELETEDENYCONFIGRESPONSE,
  '__module__' : 'live.response.response_live_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Response.DeleteDenyConfigResponse)
  })
_sym_db.RegisterMessage(DeleteDenyConfigResponse)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n*com.volcengine.service.live.model.responseB\014LiveResponseP\001ZBgithub.com/volcengine/volc-sdk-golang/service/live/models/response\240\001\001\330\001\001\312\002!Volc\\Service\\Live\\Models\\Response\342\002$Volc\\Service\\Live\\Models\\GPBMetadata'
  _CREATEDOMAINRESPONSE._serialized_start=378
  _CREATEDOMAINRESPONSE._serialized_end=473
  _LISTDOMAINDETAILRESPONSE._serialized_start=476
  _LISTDOMAINDETAILRESPONSE._serialized_end=640
  _DISABLEDOMAINRESPONSE._serialized_start=642
  _DISABLEDOMAINRESPONSE._serialized_end=738
  _ENABLEDOMAINRESPONSE._serialized_start=740
  _ENABLEDOMAINRESPONSE._serialized_end=835
  _DELETEDOMAINRESPONSE._serialized_start=837
  _DELETEDOMAINRESPONSE._serialized_end=932
  _DESCRIBEDOMAINRESPONSE._serialized_start=935
  _DESCRIBEDOMAINRESPONSE._serialized_end=1097
  _MANAGERPULLPUSHDOMAINBINDRESPONSE._serialized_start=1099
  _MANAGERPULLPUSHDOMAINBINDRESPONSE._serialized_end=1207
  _DESCRIBELIVESTREAMINFOBYPAGERESPONSE._serialized_start=1210
  _DESCRIBELIVESTREAMINFOBYPAGERESPONSE._serialized_end=1386
  _DESCRIBECLOSEDSTREAMINFOBYPAGERESPONSE._serialized_start=1389
  _DESCRIBECLOSEDSTREAMINFOBYPAGERESPONSE._serialized_end=1569
  _DESCRIBELIVESTREAMSTATERESPONSE._serialized_start=1572
  _DESCRIBELIVESTREAMSTATERESPONSE._serialized_end=1744
  _KILLSTREAMRESPONSE._serialized_start=1746
  _KILLSTREAMRESPONSE._serialized_end=1839
  _FORBIDSTREAMRESPONSE._serialized_start=1841
  _FORBIDSTREAMRESPONSE._serialized_end=1936
  _DESCRIBEFORBIDDENSTREAMINFOBYPAGERESPONSE._serialized_start=1939
  _DESCRIBEFORBIDDENSTREAMINFOBYPAGERESPONSE._serialized_end=2125
  _RESUMESTREAMRESPONSE._serialized_start=2127
  _RESUMESTREAMRESPONSE._serialized_end=2222
  _DESCRIBECDNSNAPSHOTHISTORYRESPONSE._serialized_start=2225
  _DESCRIBECDNSNAPSHOTHISTORYRESPONSE._serialized_end=2407
  _DESCRIBERECORDTASKFILEHISTORYRESPONSE._serialized_start=2410
  _DESCRIBERECORDTASKFILEHISTORYRESPONSE._serialized_end=2590
  _UPDATERELAYSOURCERESPONSE._serialized_start=2592
  _UPDATERELAYSOURCERESPONSE._serialized_end=2692
  _DELETERELAYSOURCERESPONSE._serialized_start=2694
  _DELETERELAYSOURCERESPONSE._serialized_end=2794
  _DESCRIBERELAYSOURCERESPONSE._serialized_start=2797
  _DESCRIBERELAYSOURCERESPONSE._serialized_end=2971
  _CREATEVQSCORETASKRESPONSE._serialized_start=2974
  _CREATEVQSCORETASKRESPONSE._serialized_end=3134
  _DESCRIBEVQSCORETASKRESPONSE._serialized_start=3137
  _DESCRIBEVQSCORETASKRESPONSE._serialized_end=3301
  _LISTVQSCORETASKRESPONSE._serialized_start=3304
  _LISTVQSCORETASKRESPONSE._serialized_end=3472
  _GENERATEPLAYURLRESPONSE._serialized_start=3475
  _GENERATEPLAYURLRESPONSE._serialized_end=3645
  _GENERATEPUSHURLRESPONSE._serialized_start=3648
  _GENERATEPUSHURLRESPONSE._serialized_end=3818
  _CREATEPULLTOPUSHTASKRESPONSE._serialized_start=3821
  _CREATEPULLTOPUSHTASKRESPONSE._serialized_end=4001
  _LISTPULLTOPUSHTASKRESPONSE._serialized_start=4004
  _LISTPULLTOPUSHTASKRESPONSE._serialized_end=4180
  _UPDATEPULLTOPUSHTASKRESPONSE._serialized_start=4182
  _UPDATEPULLTOPUSHTASKRESPONSE._serialized_end=4285
  _RESTARTPULLTOPUSHTASKRESPONSE._serialized_start=4287
  _RESTARTPULLTOPUSHTASKRESPONSE._serialized_end=4391
  _STOPPULLTOPUSHTASKRESPONSE._serialized_start=4393
  _STOPPULLTOPUSHTASKRESPONSE._serialized_end=4494
  _DELETEPULLTOPUSHTASKRESPONSE._serialized_start=4496
  _DELETEPULLTOPUSHTASKRESPONSE._serialized_end=4599
  _UPDATEDENYCONFIGRESPONSE._serialized_start=4601
  _UPDATEDENYCONFIGRESPONSE._serialized_end=4700
  _DESCRIBEDENYCONFIGRESPONSE._serialized_start=4703
  _DESCRIBEDENYCONFIGRESPONSE._serialized_end=4879
  _DELETEDENYCONFIGRESPONSE._serialized_start=4881
  _DELETEDENYCONFIGRESPONSE._serialized_end=4980
# @@protoc_insertion_point(module_scope)
