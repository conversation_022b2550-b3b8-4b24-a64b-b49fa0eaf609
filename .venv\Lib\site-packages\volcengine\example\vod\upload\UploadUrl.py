# coding:utf-8
from __future__ import print_function

from volcengine.vod.VodService import VodService
from volcengine.vod.models.request.request_vod_pb2 import VodUrlUploadRequest

if __name__ == '__main__':
    # Create a VOD instance in the specified region.
    # vod_service = VodService('cn-north-1')
    vod_service = VodService()

    # Configure your Access Key ID (AK) and Secret Access Key (SK) in the environment variables or in the local ~/.volc/config file. For detailed instructions, see https://www.volcengine.com/docs/4/65646.
    # The SDK will automatically fetch the AK and SK from the environment variables or the ~/.volc/config file as needed.
    # During testing, you may use the following code snippet. However, do not store the AK and SK directly in your project code to prevent potential leakage and safeguard the security of all resources associated with your account.
    # vod_service.set_ak('your ak')
    # vod_service.set_sk('your sk')

    space_name = 'your space'
    url = ''

    try:
        req = VodUrlUploadRequest()
        req.SpaceName = space_name
        url_set = req.URLSets.add()
        url_set.SourceUrl = url
        url_set.FileExtension = '.mp4'
        impTemplate = url_set.Templates.add()
        impTemplate.TemplateIds.extend(['imp template id'])
        impTemplate.TemplateType = 'imp'
        transcodeTemplate = url_set.Templates.add()
        transcodeTemplate.TemplateIds.extend(['transcode template id'])
        transcodeTemplate.TemplateType = 'transcode'
        url_set.CallbackArgs = 'my python callback args'
        customUrlHeaders = {'your header key': 'your header value'}
        url_set.CustomURLHeaders.update(**customUrlHeaders)
        resp = vod_service.upload_media_by_url(req)
    except Exception:
        raise
    else:
        print(resp)
        if resp.ResponseMetadata.Error.Code == '':
            print(resp.Result.Data)
            print(resp.Result.Data[0].JobId)
            print(resp.Result.Data[0].SourceUrl)
        else:
            print(resp.ResponseMetadata.Error)
            print(resp.ResponseMetadata.RequestId)
