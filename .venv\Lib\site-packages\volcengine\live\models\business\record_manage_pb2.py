# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: live/business/record_manage.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from volcengine.live.models.business import snapshot_manage_pb2 as live_dot_business_dot_snapshot__manage__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n!live/business/record_manage.proto\x12\x1fVolcengine.Live.Models.Business\x1a#live/business/snapshot_manage.proto\"\xbf\x01\n\x0eRecordTaskFile\x12\x0b\n\x03Vid\x18\x01 \x01(\t\x12\r\n\x05Vhost\x18\x02 \x01(\t\x12\x0b\n\x03\x41pp\x18\x03 \x01(\t\x12\x0e\n\x06Stream\x18\x04 \x01(\t\x12\x0e\n\x06\x42ucket\x18\x05 \x01(\t\x12\x0c\n\x04Path\x18\x06 \x01(\t\x12\x10\n\x08\x44uration\x18\x07 \x01(\t\x12\x11\n\tStartTime\x18\x08 \x01(\t\x12\x0e\n\x06\x46ormat\x18\t \x01(\t\x12\x0f\n\x07\x45ndTime\x18\n \x01(\t\x12\x10\n\x08\x46ileName\x18\x0b \x01(\t\"\x93\x01\n\x11RecordHistoryInfo\x12=\n\x04\x44\x61ta\x18\x01 \x03(\x0b\x32/.Volcengine.Live.Models.Business.RecordTaskFile\x12?\n\nPagination\x18\x02 \x01(\x0b\x32+.Volcengine.Live.Models.Business.PaginationB\xd4\x01\n*com.volcengine.service.live.model.businessB\x0cRecordManageP\x01ZBgithub.com/volcengine/volc-sdk-golang/service/live/models/business\xa0\x01\x01\xd8\x01\x01\xc2\x02\x00\xca\x02!Volc\\Service\\Live\\Models\\Business\xe2\x02$Volc\\Service\\Live\\Models\\GPBMetadatab\x06proto3')



_RECORDTASKFILE = DESCRIPTOR.message_types_by_name['RecordTaskFile']
_RECORDHISTORYINFO = DESCRIPTOR.message_types_by_name['RecordHistoryInfo']
RecordTaskFile = _reflection.GeneratedProtocolMessageType('RecordTaskFile', (_message.Message,), {
  'DESCRIPTOR' : _RECORDTASKFILE,
  '__module__' : 'live.business.record_manage_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Business.RecordTaskFile)
  })
_sym_db.RegisterMessage(RecordTaskFile)

RecordHistoryInfo = _reflection.GeneratedProtocolMessageType('RecordHistoryInfo', (_message.Message,), {
  'DESCRIPTOR' : _RECORDHISTORYINFO,
  '__module__' : 'live.business.record_manage_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Business.RecordHistoryInfo)
  })
_sym_db.RegisterMessage(RecordHistoryInfo)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n*com.volcengine.service.live.model.businessB\014RecordManageP\001ZBgithub.com/volcengine/volc-sdk-golang/service/live/models/business\240\001\001\330\001\001\302\002\000\312\002!Volc\\Service\\Live\\Models\\Business\342\002$Volc\\Service\\Live\\Models\\GPBMetadata'
  _RECORDTASKFILE._serialized_start=108
  _RECORDTASKFILE._serialized_end=299
  _RECORDHISTORYINFO._serialized_start=302
  _RECORDHISTORYINFO._serialized_end=449
# @@protoc_insertion_point(module_scope)
