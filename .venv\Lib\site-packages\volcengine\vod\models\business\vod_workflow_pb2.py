# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: volcengine/vod/business/vod_workflow.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from google.protobuf import timestamp_pb2 as google_dot_protobuf_dot_timestamp__pb2
from volcengine.vod.models.business import vod_common_pb2 as volcengine_dot_vod_dot_business_dot_vod__common__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n*volcengine/vod/business/vod_workflow.proto\x12\x1eVolcengine.Vod.Models.Business\x1a\x1fgoogle/protobuf/timestamp.proto\x1a(volcengine/vod/business/vod_common.proto\"\'\n\x16VodStartWorkflowResult\x12\r\n\x05RunId\x18\x01 \x01(\t\"D\n\tDirectUrl\x12\x10\n\x08\x46ileName\x18\x01 \x01(\t\x12\x12\n\nBucketName\x18\x02 \x01(\t\x12\x11\n\tSpaceName\x18\x03 \x01(\t\"\xdc\x01\n\x0eWorkflowParams\x12\x46\n\x0eOverrideParams\x18\x01 \x01(\x0b\x32..Volcengine.Vod.Models.Business.OverrideParams\x12P\n\tCondition\x18\x02 \x03(\x0b\x32=.Volcengine.Vod.Models.Business.WorkflowParams.ConditionEntry\x1a\x30\n\x0e\x43onditionEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\x08:\x02\x38\x01\"\xf2\x02\n\x0eOverrideParams\x12:\n\x04Logo\x18\x01 \x03(\x0b\x32,.Volcengine.Vod.Models.Business.LogoOverride\x12N\n\x0eTranscodeVideo\x18\x02 \x03(\x0b\x32\x36.Volcengine.Vod.Models.Business.TranscodeVideoOverride\x12N\n\x0eTranscodeAudio\x18\x03 \x03(\x0b\x32\x36.Volcengine.Vod.Models.Business.TranscodeAudioOverride\x12\x42\n\x08Snapshot\x18\x04 \x03(\x0b\x32\x30.Volcengine.Vod.Models.Business.SnapshotOverride\x12@\n\x07\x45nhance\x18\x05 \x01(\x0b\x32/.Volcengine.Vod.Models.Business.EnhanceOverride\"\x95\x01\n\x0cLogoOverride\x12\x12\n\nTemplateId\x18\x01 \x01(\t\x12\x44\n\x04Vars\x18\x02 \x03(\x0b\x32\x36.Volcengine.Vod.Models.Business.LogoOverride.VarsEntry\x1a+\n\tVarsEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\x9f\x01\n\x16TranscodeVideoOverride\x12\x12\n\nTemplateId\x18\x01 \x03(\t\x12\x32\n\x04\x43lip\x18\x02 \x01(\x0b\x32$.Volcengine.Vod.Models.Business.Clip\x12\x13\n\x0bOutputIndex\x18\x03 \x03(\x05\x12\x10\n\x08\x46ileName\x18\x04 \x01(\t\x12\x16\n\x0eLogoTemplateId\x18\x05 \x01(\t\"*\n\x04\x43lip\x12\x11\n\tStartTime\x18\x01 \x01(\x05\x12\x0f\n\x07\x45ndTime\x18\x02 \x01(\x05\"r\n\x16TranscodeAudioOverride\x12\x12\n\nTemplateId\x18\x01 \x03(\t\x12\x32\n\x04\x43lip\x18\x02 \x01(\x0b\x32$.Volcengine.Vod.Models.Business.Clip\x12\x10\n\x08\x46ileName\x18\x03 \x01(\t\"\xcc\x01\n\x10SnapshotOverride\x12\x12\n\nTemplateId\x18\x01 \x03(\t\x12\x12\n\nOffsetTime\x18\x02 \x01(\x05\x12\x16\n\x0eOffsetTimeList\x18\x03 \x03(\x05\x12\x10\n\x08\x46ileName\x18\x04 \x01(\t\x12\x11\n\tFileIndex\x18\x05 \x01(\t\x12\x15\n\rSampleOffsets\x18\x06 \x03(\x02\x12\x12\n\x05Width\x18\x07 \x01(\x05H\x00\x88\x01\x01\x12\x13\n\x06Height\x18\x08 \x01(\x05H\x01\x88\x01\x01\x42\x08\n\x06_WidthB\t\n\x07_Height\"8\n\x0f\x45nhanceOverride\x12\x13\n\x0bStorageMode\x18\x01 \x01(\t\x12\x10\n\x08\x46ileName\x18\x02 \x01(\t\"\xa5\x01\n\x0fTranscodeResult\x12\x0b\n\x03Vid\x18\x01 \x01(\t\x12>\n\nInspection\x18\x02 \x01(\x0b\x32*.Volcengine.Vod.Models.Business.Inspection\x12\x45\n\x0c\x43\x61tegoryTags\x18\x03 \x03(\x0b\x32/.Volcengine.Vod.Models.Business.CategoryTagInfo\"\x82\x01\n\nInspection\x12\x38\n\x07Quality\x18\x01 \x01(\x0b\x32\'.Volcengine.Vod.Models.Business.Quality\x12:\n\x06\x44\x65Logo\x18\x02 \x03(\x0b\x32*.Volcengine.Vod.Models.Business.DeLogoInfo\"\x88\x01\n\x07Quality\x12=\n\x06Visual\x18\x01 \x01(\x0b\x32-.Volcengine.Vod.Models.Business.VisualQuality\x12>\n\nVolumeInfo\x18\x02 \x01(\x0b\x32*.Volcengine.Vod.Models.Business.VolumeInfo\"q\n\nDeLogoInfo\x12\x13\n\x0b\x41nchorWidth\x18\x01 \x01(\x03\x12\x14\n\x0c\x41nchorHeight\x18\x02 \x01(\x03\x12\x0c\n\x04PosX\x18\x03 \x01(\x03\x12\x0c\n\x04PosY\x18\x04 \x01(\x03\x12\r\n\x05SizeX\x18\x05 \x01(\x03\x12\r\n\x05SizeY\x18\x06 \x01(\x03\"|\n\rVisualQuality\x12\x0f\n\x07VQScore\x18\x01 \x01(\x01\x12\x10\n\x08\x43ontrast\x18\x02 \x01(\x01\x12\x14\n\x0c\x43olorfulness\x18\x03 \x01(\x01\x12\x12\n\nBrightness\x18\x04 \x01(\x01\x12\x0f\n\x07Texture\x18\x05 \x01(\x01\x12\r\n\x05Noise\x18\x06 \x01(\x01\"S\n\nVolumeInfo\x12\x10\n\x08Loudness\x18\x01 \x01(\x01\x12\x0c\n\x04Peak\x18\x02 \x01(\x01\x12\x12\n\nMeanVolume\x18\x03 \x01(\x01\x12\x11\n\tMaxVolume\x18\x04 \x01(\x01\"\xd6\x01\n\x0f\x43\x61tegoryTagInfo\x12\r\n\x05TagId\x18\x01 \x01(\x03\x12\x0c\n\x04Prob\x18\x02 \x01(\x01\x12\x0f\n\x07TagName\x18\x03 \x01(\t\x12\r\n\x05Level\x18\x04 \x01(\x03\x12S\n\nParentInfo\x18\x05 \x03(\x0b\x32?.Volcengine.Vod.Models.Business.CategoryTagInfo.ParentInfoEntry\x1a\x31\n\x0fParentInfoEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\x97\x01\n\x1eVodListWorkflowExecutionResult\x12?\n\x04\x44\x61ta\x18\x01 \x03(\x0b\x32\x31.Volcengine.Vod.Models.Business.WorkflowExecution\x12\x12\n\nTotalCount\x18\x02 \x01(\x05\x12\x10\n\x08PageSize\x18\x03 \x01(\x05\x12\x0e\n\x06Offset\x18\x04 \x01(\x05\"\xb0\x04\n\x11WorkflowExecution\x12\r\n\x05RunId\x18\x01 \x01(\t\x12\x0b\n\x03Vid\x18\x02 \x01(\t\x12\x12\n\nTemplateId\x18\x03 \x01(\t\x12\x14\n\x0cTemplateName\x18\x04 \x01(\t\x12\x11\n\tSpaceName\x18\x05 \x01(\t\x12\x0e\n\x06Status\x18\x06 \x01(\t\x12\x12\n\nTaskListId\x18\x07 \x01(\t\x12\x19\n\x11\x45nableLowPriority\x18\x08 \x01(\x08\x12\x11\n\tJobSource\x18\t \x01(\t\x12.\n\nCreateTime\x18\n \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12-\n\tStartTime\x18\x0b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12+\n\x07\x45ndTime\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12=\n\x05Input\x18\r \x01(\x0b\x32..Volcengine.Vod.Models.Business.WorkflowParams\x12\x10\n\x08Priority\x18\x0e \x01(\x05\x12\x14\n\x0c\x43\x61llbackArgs\x18\x0f \x01(\t\x12?\n\x0bTasksDetail\x18\x10 \x03(\x0b\x32*.Volcengine.Vod.Models.Business.TaskDetail\x12<\n\tDirectUrl\x18\x11 \x01(\x0b\x32).Volcengine.Vod.Models.Business.DirectUrl\"\xc4\x03\n#VodGetWorkflowExecutionDetailResult\x12\r\n\x05RunId\x18\x01 \x01(\t\x12\x0b\n\x03Vid\x18\x02 \x01(\t\x12\x12\n\nTemplateId\x18\x03 \x01(\t\x12\x11\n\tSpaceName\x18\x04 \x01(\t\x12\x0e\n\x06Status\x18\x06 \x01(\t\x12\x12\n\nTaskListId\x18\x07 \x01(\t\x12\x19\n\x11\x45nableLowPriority\x18\x08 \x01(\x08\x12\x11\n\tJobSource\x18\t \x01(\t\x12>\n\x06Stages\x18\n \x03(\x0b\x32..Volcengine.Vod.Models.Business.ExecutionStage\x12.\n\nCreateTime\x18\x0b \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12-\n\tStartTime\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12+\n\x07\x45ndTime\x18\r \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12<\n\tDirectUrl\x18\x0e \x01(\x0b\x32).Volcengine.Vod.Models.Business.DirectUrl\"\xc3\x01\n\x0e\x45xecutionStage\x12\x13\n\x0b\x44isplayName\x18\x01 \x01(\t\x12@\n\x0bStageDetail\x18\x02 \x03(\x0b\x32+.Volcengine.Vod.Models.Business.StageDetail\x12-\n\tStartTime\x18\x03 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12+\n\x07\x45ndTime\x18\x04 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"\x8d\x02\n\x0bStageDetail\x12\n\n\x02Id\x18\x01 \x01(\t\x12\x13\n\x0b\x44isplayName\x18\x02 \x01(\t\x12\x0c\n\x04Type\x18\x03 \x01(\t\x12\x12\n\nTemplateId\x18\x04 \x01(\t\x12;\n\x06Status\x18\x05 \x01(\x0e\x32+.Volcengine.Vod.Models.Business.StageStatus\x12\x11\n\tErrorCode\x18\x06 \x01(\x03\x12\x0f\n\x07Message\x18\x07 \x01(\t\x12-\n\tStartTime\x18\x08 \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12+\n\x07\x45ndTime\x18\t \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"\xe0\x01\n\nTaskDetail\x12\x13\n\x0b\x44isplayName\x18\x02 \x01(\t\x12\x12\n\nTemplateId\x18\x04 \x01(\t\x12;\n\x06Status\x18\x05 \x01(\x0e\x32+.Volcengine.Vod.Models.Business.StageStatus\x12\x10\n\x08Progress\x18\x08 \x01(\x05\x12-\n\tStartTime\x18\t \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12+\n\x07\x45ndTime\x18\n \x01(\x0b\x32\x1a.google.protobuf.Timestamp\"W\n\x14SnapshotParamsPoster\x12\x0e\n\x06\x46ormat\x18\x01 \x01(\t\x12\x10\n\x08StoreUri\x18\x02 \x01(\t\x12\r\n\x05Width\x18\x03 \x01(\x05\x12\x0e\n\x06Height\x18\x04 \x01(\x05\"X\n\x15SnapshotParamsDynpost\x12\x0e\n\x06\x46ormat\x18\x01 \x01(\t\x12\x10\n\x08StoreUri\x18\x02 \x01(\t\x12\r\n\x05Width\x18\x03 \x01(\x05\x12\x0e\n\x06Height\x18\x04 \x01(\x05\"Z\n\x17SnapshotParamsAIDynpost\x12\x0e\n\x06\x46ormat\x18\x01 \x01(\t\x12\x10\n\x08StoreUri\x18\x02 \x01(\t\x12\r\n\x05Width\x18\x03 \x01(\x05\x12\x0e\n\x06Height\x18\x04 \x01(\x05\"_\n\x1cSnapshotParamsAnimatedPoster\x12\x0e\n\x06\x46ormat\x18\x01 \x01(\t\x12\x10\n\x08StoreUri\x18\x02 \x01(\t\x12\r\n\x05Width\x18\x03 \x01(\x05\x12\x0e\n\x06Height\x18\x04 \x01(\x05\"\xa8\x01\n\x14SnapshotParamsSprite\x12\x0e\n\x06\x46ormat\x18\x01 \x01(\t\x12\x11\n\tStoreUris\x18\x02 \x03(\t\x12\x11\n\tCellWidth\x18\x03 \x01(\x05\x12\x12\n\nCellHeight\x18\x04 \x01(\x05\x12\x0f\n\x07ImgXLen\x18\x05 \x01(\x05\x12\x0f\n\x07ImgYLen\x18\x06 \x01(\x05\x12\x10\n\x08Interval\x18\x07 \x01(\x02\x12\x12\n\nCaptureNum\x18\x08 \x01(\x05\"\xb3\x01\n\x14SnapshotParamsSample\x12\x0e\n\x06\x46ormat\x18\x01 \x01(\t\x12\x11\n\tStoreUris\x18\x02 \x03(\t\x12\r\n\x05Width\x18\x03 \x01(\x05\x12\x0e\n\x06Height\x18\x04 \x01(\x05\x12\x10\n\x08Interval\x18\x05 \x01(\x02\x12\x12\n\nCaptureNum\x18\x06 \x01(\x05\x12\x10\n\x08\x44uration\x18\x07 \x01(\x02\x12\x10\n\x08IndexUri\x18\x08 \x01(\t\x12\x0f\n\x07Offsets\x18\t \x03(\x02\"\xf8\x03\n\x0eSnapshotResult\x12\x0c\n\x04Type\x18\x01 \x01(\t\x12\x46\n\x06Poster\x18\x02 \x01(\x0b\x32\x34.Volcengine.Vod.Models.Business.SnapshotParamsPosterH\x00\x12H\n\x07\x44ynpost\x18\x03 \x01(\x0b\x32\x35.Volcengine.Vod.Models.Business.SnapshotParamsDynpostH\x00\x12V\n\x0e\x41nimatedPoster\x18\x04 \x01(\x0b\x32<.Volcengine.Vod.Models.Business.SnapshotParamsAnimatedPosterH\x00\x12L\n\tAIDynpost\x18\x05 \x01(\x0b\x32\x37.Volcengine.Vod.Models.Business.SnapshotParamsAIDynpostH\x00\x12\x46\n\x06Sprite\x18\x06 \x01(\x0b\x32\x34.Volcengine.Vod.Models.Business.SnapshotParamsSpriteH\x00\x12\x46\n\x06Sample\x18\x07 \x01(\x0b\x32\x34.Volcengine.Vod.Models.Business.SnapshotParamsSampleH\x00\x42\x10\n\x0eSnapshotParams\"\xdc\x02\n\x11VodWorkflowResult\x12<\n\tDirectUrl\x18\x01 \x01(\x0b\x32).Volcengine.Vod.Models.Business.DirectUrl\x12\x0b\n\x03Vid\x18\x02 \x01(\t\x12\r\n\x05RunId\x18\x03 \x01(\t\x12\x11\n\tSpaceName\x18\x04 \x01(\t\x12\x12\n\nTemplateId\x18\x05 \x01(\t\x12\x14\n\x0c\x43\x61llbackArgs\x18\x06 \x01(\t\x12\x0e\n\x06Status\x18\x07 \x01(\t\x12H\n\x0eTranscodeInfos\x18\x08 \x03(\x0b\x32\x30.Volcengine.Vod.Models.Business.VodTranscodeInfo\x12\x41\n\tSnapshots\x18\t \x03(\x0b\x32..Volcengine.Vod.Models.Business.SnapshotResult\x12\x13\n\x0b\x43lientToken\x18\n \x01(\t\"\xca\x04\n\x0cTaskTemplate\x12\x12\n\nTemplateId\x18\x01 \x01(\t\x12\x11\n\tSpaceName\x18\x02 \x01(\t\x12\x0c\n\x04Name\x18\x03 \x01(\t\x12\x13\n\x0b\x44\x65scription\x18\x04 \x01(\t\x12\x0c\n\x04Type\x18\x06 \x01(\t\x12\x10\n\x08\x43ommitId\x18\t \x01(\t\x12\x0c\n\x04Hash\x18\x0b \x01(\t\x12-\n\tCreatedAt\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12-\n\tUpdatedAt\x18\r \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x10\n\x08TaskType\x18\x0e \x01(\t\x12Z\n\x18TranscodeVideoTaskParams\x18\x0f \x01(\x0b\x32\x38.Volcengine.Vod.Models.Business.TranscodeVideoTaskParams\x12J\n\x10\x42yteHDTaskParams\x18\x12 \x01(\x0b\x32\x30.Volcengine.Vod.Models.Business.ByteHDTaskParams\x12Z\n\x18TranscodeAudioTaskParams\x18\x13 \x01(\x0b\x32\x38.Volcengine.Vod.Models.Business.TranscodeAudioTaskParams\x12N\n\x12SnapshotTaskParams\x18\x14 \x01(\x0b\x32\x32.Volcengine.Vod.Models.Business.SnapshotTaskParams\"\xc4\x03\n\x18TranscodeVideoTaskParams\x12\x1d\n\x15TranscodeTemplateType\x18\x01 \x01(\t\x12\x11\n\tContainer\x18\x02 \x01(\t\x12\x34\n\x05Video\x18\x03 \x01(\x0b\x32%.Volcengine.Vod.Models.Business.Video\x12\x34\n\x05\x41udio\x18\x04 \x01(\x0b\x32%.Volcengine.Vod.Models.Business.Audio\x12\x14\n\x0c\x44isableAudio\x18\x05 \x01(\x08\x12\x0f\n\x07Quality\x18\x06 \x01(\t\x12\x0f\n\x07Vladder\x18\x07 \x01(\t\x12\x0f\n\x07UserTag\x18\x08 \x01(\t\x12\x0f\n\x07\x45ncrypt\x18\t \x01(\x08\x12>\n\nEncryption\x18\n \x01(\x0b\x32*.Volcengine.Vod.Models.Business.Encryption\x12\x38\n\x07Segment\x18\x0b \x01(\x0b\x32\'.Volcengine.Vod.Models.Business.Segment\x12\x36\n\x06Volume\x18\x0c \x01(\x0b\x32&.Volcengine.Vod.Models.Business.Volume\"\x9d\x03\n\x10\x42yteHDTaskParams\x12\x11\n\tContainer\x18\x01 \x01(\t\x12\x34\n\x05Video\x18\x02 \x01(\x0b\x32%.Volcengine.Vod.Models.Business.Video\x12\x34\n\x05\x41udio\x18\x03 \x01(\x0b\x32%.Volcengine.Vod.Models.Business.Audio\x12\x14\n\x0c\x44isableAudio\x18\x04 \x01(\x08\x12\x0f\n\x07Quality\x18\x05 \x01(\t\x12\x0f\n\x07Vladder\x18\x06 \x01(\t\x12\x0f\n\x07UserTag\x18\x07 \x01(\t\x12\x0f\n\x07\x45ncrypt\x18\x08 \x01(\x08\x12>\n\nEncryption\x18\t \x01(\x0b\x32*.Volcengine.Vod.Models.Business.Encryption\x12\x38\n\x07Segment\x18\n \x01(\x0b\x32\'.Volcengine.Vod.Models.Business.Segment\x12\x36\n\x06Volume\x18\x0b \x01(\x0b\x32&.Volcengine.Vod.Models.Business.Volume\"\xc8\x02\n\x18TranscodeAudioTaskParams\x12\x11\n\tContainer\x18\x01 \x01(\t\x12\x34\n\x05\x41udio\x18\x02 \x01(\x0b\x32%.Volcengine.Vod.Models.Business.Audio\x12\x0f\n\x07Quality\x18\x03 \x01(\t\x12\x0f\n\x07UserTag\x18\x04 \x01(\t\x12\x0f\n\x07\x45ncrypt\x18\x05 \x01(\x08\x12>\n\nEncryption\x18\x06 \x01(\x0b\x32*.Volcengine.Vod.Models.Business.Encryption\x12\x38\n\x07Segment\x18\x07 \x01(\x0b\x32\'.Volcengine.Vod.Models.Business.Segment\x12\x36\n\x06Volume\x18\x08 \x01(\x0b\x32&.Volcengine.Vod.Models.Business.Volume\"\xd8\x03\n\x12SnapshotTaskParams\x12\x0c\n\x04Type\x18\x01 \x01(\t\x12R\n\x14PosterSnapshotParams\x18\x02 \x01(\x0b\x32\x34.Volcengine.Vod.Models.Business.PosterSnapshotParams\x12T\n\x15\x44ynpostSnapshotParams\x18\x03 \x01(\x0b\x32\x35.Volcengine.Vod.Models.Business.DynpostSnapshotParams\x12\x62\n\x1c\x41nimatedPosterSnapshotParams\x18\x04 \x01(\x0b\x32<.Volcengine.Vod.Models.Business.AnimatedPosterSnapshotParams\x12R\n\x14SpriteSnapshotParams\x18\x05 \x01(\x0b\x32\x34.Volcengine.Vod.Models.Business.SpriteSnapshotParams\x12R\n\x14SampleSnapshotParams\x18\x07 \x01(\x0b\x32\x34.Volcengine.Vod.Models.Business.SampleSnapshotParams\"\x8f\x01\n\x14PosterSnapshotParams\x12\x0e\n\x06\x46ormat\x18\x01 \x01(\t\x12\x10\n\x08ResAdapt\x18\x02 \x01(\x08\x12\x10\n\x08ResLimit\x18\x03 \x01(\x05\x12\r\n\x05Width\x18\x04 \x01(\x05\x12\x0e\n\x06Height\x18\x05 \x01(\x05\x12\x12\n\nOffsetTime\x18\x06 \x01(\x05\x12\x10\n\x08\x46illType\x18\x07 \x01(\t\"\xc5\x01\n\x15\x44ynpostSnapshotParams\x12\x0e\n\x06\x46ormat\x18\x01 \x01(\t\x12\x10\n\x08ResAdapt\x18\x02 \x01(\x08\x12\x10\n\x08ResLimit\x18\x03 \x01(\x05\x12\r\n\x05Width\x18\x04 \x01(\x05\x12\x0e\n\x06Height\x18\x05 \x01(\x05\x12\x12\n\nOffsetTime\x18\x06 \x01(\x05\x12\x10\n\x08\x44uration\x18\x07 \x01(\x05\x12\x12\n\nCaptureFps\x18\x08 \x01(\x02\x12\r\n\x05Speed\x18\t \x01(\x02\x12\x10\n\x08\x46illType\x18\n \x01(\t\"\xbf\x01\n\x1c\x41nimatedPosterSnapshotParams\x12\x0e\n\x06\x46ormat\x18\x01 \x01(\t\x12\x10\n\x08ResAdapt\x18\x02 \x01(\x08\x12\x10\n\x08ResLimit\x18\x03 \x01(\x05\x12\r\n\x05Width\x18\x04 \x01(\x05\x12\x0e\n\x06Height\x18\x05 \x01(\x05\x12\x12\n\nOffsetTime\x18\x06 \x01(\x05\x12\x12\n\nCaptureFps\x18\x07 \x01(\x02\x12\x12\n\nCaptureNum\x18\x08 \x01(\x05\x12\x10\n\x08\x46illType\x18\t \x01(\t\"\xdf\x01\n\x14SpriteSnapshotParams\x12\x0e\n\x06\x46ormat\x18\x01 \x01(\t\x12\x11\n\tCellWidth\x18\x02 \x01(\x05\x12\x12\n\nCellHeight\x18\x03 \x01(\x05\x12\x0f\n\x07ImgXLen\x18\x04 \x01(\x05\x12\x0f\n\x07ImgYLen\x18\x05 \x01(\x05\x12\x10\n\x08Interval\x18\x06 \x01(\x05\x12\x12\n\nOffsetTime\x18\x07 \x01(\x05\x12\x12\n\nCaptureNum\x18\x08 \x01(\x05\x12\x10\n\x08ResAdapt\x18\t \x01(\x08\x12\x10\n\x08ResLimit\x18\n \x01(\x05\x12\x10\n\x08\x46illType\x18\x0b \x01(\t\"\xd8\x01\n\x14SampleSnapshotParams\x12\x0e\n\x06\x46ormat\x18\x01 \x01(\t\x12\x10\n\x08ResAdapt\x18\x02 \x01(\x08\x12\x10\n\x08ResLimit\x18\x03 \x01(\x05\x12\r\n\x05Width\x18\x04 \x01(\x05\x12\x0e\n\x06Height\x18\x05 \x01(\x05\x12\x12\n\nCaptureNum\x18\x06 \x01(\x05\x12\x13\n\x0b\x43\x61ptureMode\x18\x07 \x01(\x05\x12\x10\n\x08Interval\x18\x08 \x01(\x01\x12\x0f\n\x07OutMode\x18\t \x01(\t\x12\x10\n\x08\x46illType\x18\n \x01(\t\x12\x0f\n\x07Offsets\x18\x0b \x03(\x02\"[\n\x15VodTaskTemplateResult\x12\x42\n\x0cTaskTemplate\x18\x02 \x01(\x0b\x32,.Volcengine.Vod.Models.Business.TaskTemplate\"\x85\x01\n\x19VodListTaskTemplateResult\x12\r\n\x05Limit\x18\x01 \x01(\x05\x12\x0e\n\x06Offset\x18\x02 \x01(\x05\x12\r\n\x05Total\x18\x03 \x01(\x03\x12:\n\x04\x44\x61ta\x18\x04 \x03(\x0b\x32,.Volcengine.Vod.Models.Business.TaskTemplate\"\x98\x02\n\x10WorkflowTemplate\x12\x12\n\nTemplateId\x18\x01 \x01(\t\x12\x11\n\tSpaceName\x18\x02 \x01(\t\x12\x0c\n\x04Name\x18\x03 \x01(\t\x12\x13\n\x0b\x44\x65scription\x18\x04 \x01(\t\x12\x0c\n\x04Type\x18\x06 \x01(\t\x12-\n\tCreatedAt\x18\x0c \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12-\n\tUpdatedAt\x18\r \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x10\n\x08\x43ommitId\x18\x0e \x01(\t\x12<\n\nActivities\x18\x13 \x03(\x0b\x32(.Volcengine.Vod.Models.Business.Activity\"\xab\x02\n\x08\x41\x63tivity\x12\x12\n\nActivityId\x18\x01 \x01(\t\x12\x0c\n\x04Name\x18\x02 \x01(\t\x12\x13\n\x0b\x44\x65scription\x18\x03 \x01(\t\x12\x0c\n\x04Type\x18\x04 \x01(\t\x12J\n\x10SnapshotActivity\x18\x0c \x01(\x0b\x32\x30.Volcengine.Vod.Models.Business.SnapshotActivity\x12@\n\x0b\x45ndActivity\x18\x13 \x01(\x0b\x32+.Volcengine.Vod.Models.Business.EndActivity\x12L\n\x11TranscodeActivity\x18\x17 \x01(\x0b\x32\x31.Volcengine.Vod.Models.Business.TranscodeActivity\"\xe5\x03\n\x11TranscodeActivity\x12\x12\n\nTemplateId\x18\x01 \x01(\t\x12P\n\x07\x45nhance\x18\x02 \x01(\x0b\x32?.Volcengine.Vod.Models.Business.TranscodeActivity.EnhanceParams\x12J\n\x04Logo\x18\x03 \x01(\x0b\x32<.Volcengine.Vod.Models.Business.TranscodeActivity.LogoParams\x12\x10\n\x08\x46ileName\x18\x05 \x01(\t\x12@\n\x08Parallel\x18\x06 \x01(\x0b\x32..Volcengine.Vod.Models.Business.ParallelParams\x12<\n\tCondition\x18\x07 \x01(\x0b\x32).Volcengine.Vod.Models.Business.Condition\x1a\x34\n\rEnhanceParams\x12\x12\n\nTemplateId\x18\x01 \x01(\t\x12\x0f\n\x07Version\x18\x02 \x01(\t\x1a \n\nLogoParams\x12\x12\n\nTemplateId\x18\x01 \x01(\t\x1a\x34\n\x0eSubtitleParams\x12\x10\n\x08Language\x18\x01 \x01(\t\x12\x10\n\x08\x46ontType\x18\x02 \x01(\t\"v\n\x10SnapshotActivity\x12\x12\n\nTemplateId\x18\x01 \x01(\t\x12\x10\n\x08\x46ileName\x18\x02 \x01(\t\x12<\n\tCondition\x18\x03 \x01(\x0b\x32).Volcengine.Vod.Models.Business.Condition\"%\n\x0b\x45ndActivity\x12\x16\n\x0eTranscodeEvent\x18\x01 \x01(\t\"g\n\x19VodWorkflowTemplateResult\x12J\n\x10WorkflowTemplate\x18\x02 \x01(\x0b\x32\x30.Volcengine.Vod.Models.Business.WorkflowTemplate\"\x8d\x01\n\x1dVodListWorkflowTemplateResult\x12\r\n\x05Limit\x18\x01 \x01(\x05\x12\x0e\n\x06Offset\x18\x02 \x01(\x05\x12\r\n\x05Total\x18\x03 \x01(\x03\x12>\n\x04\x44\x61ta\x18\x04 \x03(\x0b\x32\x30.Volcengine.Vod.Models.Business.WorkflowTemplate\"\xa9\x03\n\x0cLogoTemplate\x12\x12\n\nTemplateId\x18\x01 \x01(\t\x12\x11\n\tSpaceName\x18\x02 \x01(\t\x12\x0c\n\x04Name\x18\x03 \x01(\t\x12\x13\n\x0b\x44\x65scription\x18\x04 \x01(\t\x12\x0c\n\x04Type\x18\x06 \x01(\t\x12-\n\tCreatedAt\x18\t \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12-\n\tUpdatedAt\x18\n \x01(\x0b\x32\x1a.google.protobuf.Timestamp\x12\x0c\n\x04Hash\x18\x0b \x01(\t\x12\x10\n\x08LogoType\x18\x0c \x01(\t\x12\x38\n\x05Logos\x18\r \x03(\x0b\x32).Volcengine.Vod.Models.Business.AdaptLogo\x12<\n\x07\x43oncats\x18\x0e \x03(\x0b\x32+.Volcengine.Vod.Models.Business.AdaptConcat\x12K\n\x0fHiddenWatermark\x18\x0f \x01(\x0b\x32\x32.Volcengine.Vod.Models.Business.HiddenWatermarkAdd\"\x8a\x01\n\tAdaptLogo\x12>\n\nAnchorSize\x18\x01 \x01(\x0b\x32*.Volcengine.Vod.Models.Business.AnchorSize\x12=\n\x05Logos\x18\x02 \x03(\x0b\x32..Volcengine.Vod.Models.Business.LogoDefinition\"+\n\nAnchorSize\x12\r\n\x05Width\x18\x01 \x01(\x05\x12\x0e\n\x06Height\x18\x02 \x01(\x05\"\x9b\x02\n\x13ImageLogoDefinition\x12\x0b\n\x03Mid\x18\x01 \x01(\t\x12\x11\n\tStartTime\x18\x02 \x01(\x05\x12\x0f\n\x07\x45ndTime\x18\x03 \x01(\x05\x12\x0e\n\x06Locate\x18\x04 \x01(\t\x12\x0c\n\x04PosX\x18\x05 \x01(\x05\x12\x0c\n\x04PosY\x18\x06 \x01(\x05\x12\r\n\x05SizeX\x18\x07 \x01(\x05\x12\r\n\x05SizeY\x18\x08 \x01(\x05\x12\x11\n\tLoopTimes\x18\t \x01(\x05\x12\x12\n\nRepeatLast\x18\n \x01(\x08\x12\x14\n\x0cTransparency\x18\x0b \x01(\x05\x12\x11\n\tPosRatioX\x18\x0c \x01(\x01\x12\x11\n\tPosRatioY\x18\r \x01(\x01\x12\x12\n\nSizeRatioX\x18\x0e \x01(\x01\x12\x12\n\nSizeRatioY\x18\x0f \x01(\x01\"\x9b\x02\n\x13VideoLogoDefinition\x12\x0b\n\x03Mid\x18\x01 \x01(\t\x12\x11\n\tStartTime\x18\x02 \x01(\x05\x12\x0f\n\x07\x45ndTime\x18\x03 \x01(\x05\x12\x11\n\tLoopTimes\x18\x04 \x01(\x05\x12\x12\n\nRepeatLast\x18\x05 \x01(\x08\x12\x0e\n\x06Locate\x18\x06 \x01(\t\x12\x0c\n\x04PosX\x18\x07 \x01(\x05\x12\x0c\n\x04PosY\x18\x08 \x01(\x05\x12\r\n\x05SizeX\x18\t \x01(\x05\x12\r\n\x05SizeY\x18\n \x01(\x05\x12\x14\n\x0cTransparency\x18\x0b \x01(\x05\x12\x11\n\tPosRatioX\x18\x0c \x01(\x01\x12\x11\n\tPosRatioY\x18\r \x01(\x01\x12\x12\n\nSizeRatioX\x18\x0e \x01(\x01\x12\x12\n\nSizeRatioY\x18\x0f \x01(\x01\"\x98\x02\n\x12TextLogoDefinition\x12\x0f\n\x07\x43ontent\x18\x01 \x01(\t\x12\x10\n\x08\x46ontType\x18\x02 \x01(\t\x12\x10\n\x08\x46ontSize\x18\x03 \x01(\x05\x12\x11\n\tFontColor\x18\x04 \x01(\t\x12\x11\n\tStartTime\x18\x05 \x01(\x05\x12\x0f\n\x07\x45ndTime\x18\x06 \x01(\x05\x12\x0e\n\x06Locate\x18\x07 \x01(\t\x12\x0c\n\x04PosX\x18\x08 \x01(\x05\x12\x0c\n\x04PosY\x18\t \x01(\x05\x12\r\n\x05SizeX\x18\n \x01(\x05\x12\r\n\x05SizeY\x18\x0b \x01(\x05\x12\x11\n\tPosRatioX\x18\x0c \x01(\x01\x12\x11\n\tPosRatioY\x18\r \x01(\x01\x12\x12\n\nSizeRatioX\x18\x0e \x01(\x01\x12\x12\n\nSizeRatioY\x18\x0f \x01(\x01\"\x92\x02\n\x0eLogoDefinition\x12\x0c\n\x04Type\x18\x01 \x01(\t\x12P\n\x13ImageLogoDefinition\x18\x02 \x01(\x0b\x32\x33.Volcengine.Vod.Models.Business.ImageLogoDefinition\x12P\n\x13VideoLogoDefinition\x18\x03 \x01(\x0b\x32\x33.Volcengine.Vod.Models.Business.VideoLogoDefinition\x12N\n\x12TextLogoDefinition\x18\x04 \x01(\x0b\x32\x32.Volcengine.Vod.Models.Business.TextLogoDefinition\"=\n\nFontShadow\x12\r\n\x05\x43olor\x18\x01 \x01(\t\x12\x0f\n\x07OffsetX\x18\x02 \x01(\x05\x12\x0f\n\x07OffsetY\x18\x03 \x01(\x05\"\x90\x01\n\x0b\x41\x64\x61ptConcat\x12>\n\nAnchorSize\x18\x01 \x01(\x0b\x32*.Volcengine.Vod.Models.Business.AnchorSize\x12\x41\n\x07\x43oncats\x18\x02 \x03(\x0b\x32\x30.Volcengine.Vod.Models.Business.ConcatDefinition\"?\n\x10\x43oncatDefinition\x12\x0c\n\x04Type\x18\x01 \x01(\t\x12\x0b\n\x03Mid\x18\x02 \x01(\t\x12\x10\n\x08Position\x18\x04 \x01(\t\"3\n\x12HiddenWatermarkAdd\x12\x0f\n\x07\x43ontent\x18\x01 \x01(\t\x12\x0c\n\x04Type\x18\x03 \x01(\t\"\x8a\x01\n\x1eVodListWatermarkResponseResult\x12\r\n\x05Limit\x18\x01 \x01(\x05\x12\x0e\n\x06Offset\x18\x02 \x01(\x05\x12\r\n\x05Total\x18\x03 \x01(\x03\x12:\n\x04\x44\x61ta\x18\x04 \x03(\x0b\x32,.Volcengine.Vod.Models.Business.LogoTemplate\"\xc9\x02\n\x05Video\x12\x0b\n\x03Res\x18\x01 \x01(\t\x12\x11\n\tScaleType\x18\x04 \x01(\x05\x12\x11\n\tScaleMode\x18\x16 \x01(\x05\x12\x12\n\nScaleWidth\x18\x05 \x01(\x05\x12\x13\n\x0bScaleHeight\x18\x06 \x01(\x05\x12\x12\n\nScaleShort\x18\x07 \x01(\x05\x12\x11\n\tScaleLong\x18\x08 \x01(\x05\x12\r\n\x05\x43odec\x18\t \x01(\t\x12\x17\n\x0fRateControlMode\x18\n \x01(\t\x12\x12\n\nMaxBitrate\x18\x0b \x01(\x05\x12\x0f\n\x07\x42itrate\x18\x0c \x01(\x05\x12\x0b\n\x03\x43rf\x18\r \x01(\x02\x12\x0e\n\x06MaxFps\x18\x0f \x01(\x05\x12\r\n\x05Vsync\x18\x10 \x01(\t\x12\x0b\n\x03\x46ps\x18\x11 \x01(\x02\x12\x0f\n\x07HDRMode\x18\x15 \x01(\x05\x12\x0f\n\x07GopSize\x18\x18 \x01(\x05\x12\x15\n\rDisableBFrame\x18\x19 \x01(\x08\"\x99\x01\n\x05\x41udio\x12\r\n\x05\x43odec\x18\x01 \x01(\t\x12\x12\n\nSampleRate\x18\x02 \x01(\x05\x12\x17\n\x0fRateControlMode\x18\x03 \x01(\t\x12\x0f\n\x07\x42itrate\x18\x04 \x01(\x05\x12\x0f\n\x07MinRate\x18\x05 \x01(\x05\x12\x0f\n\x07MaxRate\x18\x06 \x01(\x05\x12\x10\n\x08\x43hannels\x18\x07 \x01(\x05\x12\x0f\n\x07Profile\x18\x08 \x01(\t\"9\n\x07Segment\x12\x0e\n\x06\x46ormat\x18\x01 \x01(\t\x12\x0c\n\x04Type\x18\x02 \x01(\t\x12\x10\n\x08\x44uration\x18\x03 \x01(\x05\"\x84\x02\n\tCondition\x12\x10\n\x08ResRange\x18\x01 \x01(\t\x12\x14\n\x0cLongResRange\x18\x02 \x01(\t\x12\x15\n\rDurationRange\x18\x03 \x01(\t\x12\x10\n\x08\x46psRange\x18\x04 \x01(\t\x12\x14\n\x0c\x42itrateRange\x18\x05 \x01(\t\x12\x19\n\x11\x41udioBitrateRange\x18\x06 \x01(\t\x12\x10\n\x08\x46ileType\x18\x07 \x01(\t\x12\x14\n\x0cVQScoreRange\x18\x08 \x01(\t\x12\x1a\n\x12VideoDurationRange\x18\t \x01(\t\x12\x1a\n\x12\x41udioDurationRange\x18\n \x01(\t\x12\x15\n\rUserCondition\x18\x0b \x01(\t\"!\n\x0eParallelParams\x12\x0f\n\x07\x45nabled\x18\x01 \x01(\x08\"\x1c\n\nEncryption\x12\x0e\n\x06Vendor\x18\x01 \x01(\t\"j\n\x06Volume\x12\x0e\n\x06Method\x18\x01 \x01(\t\x12\x1a\n\x12IntegratedLoudness\x18\x02 \x01(\x01\x12\x10\n\x08TruePeak\x18\x03 \x01(\x01\x12\x0e\n\x06Volume\x18\x04 \x01(\x01\x12\x12\n\nVolumeTime\x18\x05 \x01(\x01*z\n\x0bStageStatus\x12\x0b\n\x07Unknown\x10\x00\x12\r\n\tScheduled\x10\x01\x12\x0b\n\x07Running\x10\x02\x12\x0c\n\x08\x43\x61nceled\x10\x03\x12\x0c\n\x08TimedOut\x10\x04\x12\x0b\n\x07Skipped\x10\x05\x12\r\n\tCompleted\x10\x06\x12\n\n\x06\x46\x61iled\x10\x07\x42\xcc\x01\n)com.volcengine.service.vod.model.businessB\x0bVodWorkflowP\x01ZAgithub.com/volcengine/volc-sdk-golang/service/vod/models/business\xa0\x01\x01\xd8\x01\x01\xca\x02 Volc\\Service\\Vod\\Models\\Business\xe2\x02#Volc\\Service\\Vod\\Models\\GPBMetadatab\x06proto3')

_STAGESTATUS = DESCRIPTOR.enum_types_by_name['StageStatus']
StageStatus = enum_type_wrapper.EnumTypeWrapper(_STAGESTATUS)
Unknown = 0
Scheduled = 1
Running = 2
Canceled = 3
TimedOut = 4
Skipped = 5
Completed = 6
Failed = 7


_VODSTARTWORKFLOWRESULT = DESCRIPTOR.message_types_by_name['VodStartWorkflowResult']
_DIRECTURL = DESCRIPTOR.message_types_by_name['DirectUrl']
_WORKFLOWPARAMS = DESCRIPTOR.message_types_by_name['WorkflowParams']
_WORKFLOWPARAMS_CONDITIONENTRY = _WORKFLOWPARAMS.nested_types_by_name['ConditionEntry']
_OVERRIDEPARAMS = DESCRIPTOR.message_types_by_name['OverrideParams']
_LOGOOVERRIDE = DESCRIPTOR.message_types_by_name['LogoOverride']
_LOGOOVERRIDE_VARSENTRY = _LOGOOVERRIDE.nested_types_by_name['VarsEntry']
_TRANSCODEVIDEOOVERRIDE = DESCRIPTOR.message_types_by_name['TranscodeVideoOverride']
_CLIP = DESCRIPTOR.message_types_by_name['Clip']
_TRANSCODEAUDIOOVERRIDE = DESCRIPTOR.message_types_by_name['TranscodeAudioOverride']
_SNAPSHOTOVERRIDE = DESCRIPTOR.message_types_by_name['SnapshotOverride']
_ENHANCEOVERRIDE = DESCRIPTOR.message_types_by_name['EnhanceOverride']
_TRANSCODERESULT = DESCRIPTOR.message_types_by_name['TranscodeResult']
_INSPECTION = DESCRIPTOR.message_types_by_name['Inspection']
_QUALITY = DESCRIPTOR.message_types_by_name['Quality']
_DELOGOINFO = DESCRIPTOR.message_types_by_name['DeLogoInfo']
_VISUALQUALITY = DESCRIPTOR.message_types_by_name['VisualQuality']
_VOLUMEINFO = DESCRIPTOR.message_types_by_name['VolumeInfo']
_CATEGORYTAGINFO = DESCRIPTOR.message_types_by_name['CategoryTagInfo']
_CATEGORYTAGINFO_PARENTINFOENTRY = _CATEGORYTAGINFO.nested_types_by_name['ParentInfoEntry']
_VODLISTWORKFLOWEXECUTIONRESULT = DESCRIPTOR.message_types_by_name['VodListWorkflowExecutionResult']
_WORKFLOWEXECUTION = DESCRIPTOR.message_types_by_name['WorkflowExecution']
_VODGETWORKFLOWEXECUTIONDETAILRESULT = DESCRIPTOR.message_types_by_name['VodGetWorkflowExecutionDetailResult']
_EXECUTIONSTAGE = DESCRIPTOR.message_types_by_name['ExecutionStage']
_STAGEDETAIL = DESCRIPTOR.message_types_by_name['StageDetail']
_TASKDETAIL = DESCRIPTOR.message_types_by_name['TaskDetail']
_SNAPSHOTPARAMSPOSTER = DESCRIPTOR.message_types_by_name['SnapshotParamsPoster']
_SNAPSHOTPARAMSDYNPOST = DESCRIPTOR.message_types_by_name['SnapshotParamsDynpost']
_SNAPSHOTPARAMSAIDYNPOST = DESCRIPTOR.message_types_by_name['SnapshotParamsAIDynpost']
_SNAPSHOTPARAMSANIMATEDPOSTER = DESCRIPTOR.message_types_by_name['SnapshotParamsAnimatedPoster']
_SNAPSHOTPARAMSSPRITE = DESCRIPTOR.message_types_by_name['SnapshotParamsSprite']
_SNAPSHOTPARAMSSAMPLE = DESCRIPTOR.message_types_by_name['SnapshotParamsSample']
_SNAPSHOTRESULT = DESCRIPTOR.message_types_by_name['SnapshotResult']
_VODWORKFLOWRESULT = DESCRIPTOR.message_types_by_name['VodWorkflowResult']
_TASKTEMPLATE = DESCRIPTOR.message_types_by_name['TaskTemplate']
_TRANSCODEVIDEOTASKPARAMS = DESCRIPTOR.message_types_by_name['TranscodeVideoTaskParams']
_BYTEHDTASKPARAMS = DESCRIPTOR.message_types_by_name['ByteHDTaskParams']
_TRANSCODEAUDIOTASKPARAMS = DESCRIPTOR.message_types_by_name['TranscodeAudioTaskParams']
_SNAPSHOTTASKPARAMS = DESCRIPTOR.message_types_by_name['SnapshotTaskParams']
_POSTERSNAPSHOTPARAMS = DESCRIPTOR.message_types_by_name['PosterSnapshotParams']
_DYNPOSTSNAPSHOTPARAMS = DESCRIPTOR.message_types_by_name['DynpostSnapshotParams']
_ANIMATEDPOSTERSNAPSHOTPARAMS = DESCRIPTOR.message_types_by_name['AnimatedPosterSnapshotParams']
_SPRITESNAPSHOTPARAMS = DESCRIPTOR.message_types_by_name['SpriteSnapshotParams']
_SAMPLESNAPSHOTPARAMS = DESCRIPTOR.message_types_by_name['SampleSnapshotParams']
_VODTASKTEMPLATERESULT = DESCRIPTOR.message_types_by_name['VodTaskTemplateResult']
_VODLISTTASKTEMPLATERESULT = DESCRIPTOR.message_types_by_name['VodListTaskTemplateResult']
_WORKFLOWTEMPLATE = DESCRIPTOR.message_types_by_name['WorkflowTemplate']
_ACTIVITY = DESCRIPTOR.message_types_by_name['Activity']
_TRANSCODEACTIVITY = DESCRIPTOR.message_types_by_name['TranscodeActivity']
_TRANSCODEACTIVITY_ENHANCEPARAMS = _TRANSCODEACTIVITY.nested_types_by_name['EnhanceParams']
_TRANSCODEACTIVITY_LOGOPARAMS = _TRANSCODEACTIVITY.nested_types_by_name['LogoParams']
_TRANSCODEACTIVITY_SUBTITLEPARAMS = _TRANSCODEACTIVITY.nested_types_by_name['SubtitleParams']
_SNAPSHOTACTIVITY = DESCRIPTOR.message_types_by_name['SnapshotActivity']
_ENDACTIVITY = DESCRIPTOR.message_types_by_name['EndActivity']
_VODWORKFLOWTEMPLATERESULT = DESCRIPTOR.message_types_by_name['VodWorkflowTemplateResult']
_VODLISTWORKFLOWTEMPLATERESULT = DESCRIPTOR.message_types_by_name['VodListWorkflowTemplateResult']
_LOGOTEMPLATE = DESCRIPTOR.message_types_by_name['LogoTemplate']
_ADAPTLOGO = DESCRIPTOR.message_types_by_name['AdaptLogo']
_ANCHORSIZE = DESCRIPTOR.message_types_by_name['AnchorSize']
_IMAGELOGODEFINITION = DESCRIPTOR.message_types_by_name['ImageLogoDefinition']
_VIDEOLOGODEFINITION = DESCRIPTOR.message_types_by_name['VideoLogoDefinition']
_TEXTLOGODEFINITION = DESCRIPTOR.message_types_by_name['TextLogoDefinition']
_LOGODEFINITION = DESCRIPTOR.message_types_by_name['LogoDefinition']
_FONTSHADOW = DESCRIPTOR.message_types_by_name['FontShadow']
_ADAPTCONCAT = DESCRIPTOR.message_types_by_name['AdaptConcat']
_CONCATDEFINITION = DESCRIPTOR.message_types_by_name['ConcatDefinition']
_HIDDENWATERMARKADD = DESCRIPTOR.message_types_by_name['HiddenWatermarkAdd']
_VODLISTWATERMARKRESPONSERESULT = DESCRIPTOR.message_types_by_name['VodListWatermarkResponseResult']
_VIDEO = DESCRIPTOR.message_types_by_name['Video']
_AUDIO = DESCRIPTOR.message_types_by_name['Audio']
_SEGMENT = DESCRIPTOR.message_types_by_name['Segment']
_CONDITION = DESCRIPTOR.message_types_by_name['Condition']
_PARALLELPARAMS = DESCRIPTOR.message_types_by_name['ParallelParams']
_ENCRYPTION = DESCRIPTOR.message_types_by_name['Encryption']
_VOLUME = DESCRIPTOR.message_types_by_name['Volume']
VodStartWorkflowResult = _reflection.GeneratedProtocolMessageType('VodStartWorkflowResult', (_message.Message,), {
  'DESCRIPTOR' : _VODSTARTWORKFLOWRESULT,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.VodStartWorkflowResult)
  })
_sym_db.RegisterMessage(VodStartWorkflowResult)

DirectUrl = _reflection.GeneratedProtocolMessageType('DirectUrl', (_message.Message,), {
  'DESCRIPTOR' : _DIRECTURL,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.DirectUrl)
  })
_sym_db.RegisterMessage(DirectUrl)

WorkflowParams = _reflection.GeneratedProtocolMessageType('WorkflowParams', (_message.Message,), {

  'ConditionEntry' : _reflection.GeneratedProtocolMessageType('ConditionEntry', (_message.Message,), {
    'DESCRIPTOR' : _WORKFLOWPARAMS_CONDITIONENTRY,
    '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
    # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.WorkflowParams.ConditionEntry)
    })
  ,
  'DESCRIPTOR' : _WORKFLOWPARAMS,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.WorkflowParams)
  })
_sym_db.RegisterMessage(WorkflowParams)
_sym_db.RegisterMessage(WorkflowParams.ConditionEntry)

OverrideParams = _reflection.GeneratedProtocolMessageType('OverrideParams', (_message.Message,), {
  'DESCRIPTOR' : _OVERRIDEPARAMS,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.OverrideParams)
  })
_sym_db.RegisterMessage(OverrideParams)

LogoOverride = _reflection.GeneratedProtocolMessageType('LogoOverride', (_message.Message,), {

  'VarsEntry' : _reflection.GeneratedProtocolMessageType('VarsEntry', (_message.Message,), {
    'DESCRIPTOR' : _LOGOOVERRIDE_VARSENTRY,
    '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
    # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.LogoOverride.VarsEntry)
    })
  ,
  'DESCRIPTOR' : _LOGOOVERRIDE,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.LogoOverride)
  })
_sym_db.RegisterMessage(LogoOverride)
_sym_db.RegisterMessage(LogoOverride.VarsEntry)

TranscodeVideoOverride = _reflection.GeneratedProtocolMessageType('TranscodeVideoOverride', (_message.Message,), {
  'DESCRIPTOR' : _TRANSCODEVIDEOOVERRIDE,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.TranscodeVideoOverride)
  })
_sym_db.RegisterMessage(TranscodeVideoOverride)

Clip = _reflection.GeneratedProtocolMessageType('Clip', (_message.Message,), {
  'DESCRIPTOR' : _CLIP,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.Clip)
  })
_sym_db.RegisterMessage(Clip)

TranscodeAudioOverride = _reflection.GeneratedProtocolMessageType('TranscodeAudioOverride', (_message.Message,), {
  'DESCRIPTOR' : _TRANSCODEAUDIOOVERRIDE,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.TranscodeAudioOverride)
  })
_sym_db.RegisterMessage(TranscodeAudioOverride)

SnapshotOverride = _reflection.GeneratedProtocolMessageType('SnapshotOverride', (_message.Message,), {
  'DESCRIPTOR' : _SNAPSHOTOVERRIDE,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.SnapshotOverride)
  })
_sym_db.RegisterMessage(SnapshotOverride)

EnhanceOverride = _reflection.GeneratedProtocolMessageType('EnhanceOverride', (_message.Message,), {
  'DESCRIPTOR' : _ENHANCEOVERRIDE,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.EnhanceOverride)
  })
_sym_db.RegisterMessage(EnhanceOverride)

TranscodeResult = _reflection.GeneratedProtocolMessageType('TranscodeResult', (_message.Message,), {
  'DESCRIPTOR' : _TRANSCODERESULT,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.TranscodeResult)
  })
_sym_db.RegisterMessage(TranscodeResult)

Inspection = _reflection.GeneratedProtocolMessageType('Inspection', (_message.Message,), {
  'DESCRIPTOR' : _INSPECTION,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.Inspection)
  })
_sym_db.RegisterMessage(Inspection)

Quality = _reflection.GeneratedProtocolMessageType('Quality', (_message.Message,), {
  'DESCRIPTOR' : _QUALITY,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.Quality)
  })
_sym_db.RegisterMessage(Quality)

DeLogoInfo = _reflection.GeneratedProtocolMessageType('DeLogoInfo', (_message.Message,), {
  'DESCRIPTOR' : _DELOGOINFO,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.DeLogoInfo)
  })
_sym_db.RegisterMessage(DeLogoInfo)

VisualQuality = _reflection.GeneratedProtocolMessageType('VisualQuality', (_message.Message,), {
  'DESCRIPTOR' : _VISUALQUALITY,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.VisualQuality)
  })
_sym_db.RegisterMessage(VisualQuality)

VolumeInfo = _reflection.GeneratedProtocolMessageType('VolumeInfo', (_message.Message,), {
  'DESCRIPTOR' : _VOLUMEINFO,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.VolumeInfo)
  })
_sym_db.RegisterMessage(VolumeInfo)

CategoryTagInfo = _reflection.GeneratedProtocolMessageType('CategoryTagInfo', (_message.Message,), {

  'ParentInfoEntry' : _reflection.GeneratedProtocolMessageType('ParentInfoEntry', (_message.Message,), {
    'DESCRIPTOR' : _CATEGORYTAGINFO_PARENTINFOENTRY,
    '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
    # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.CategoryTagInfo.ParentInfoEntry)
    })
  ,
  'DESCRIPTOR' : _CATEGORYTAGINFO,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.CategoryTagInfo)
  })
_sym_db.RegisterMessage(CategoryTagInfo)
_sym_db.RegisterMessage(CategoryTagInfo.ParentInfoEntry)

VodListWorkflowExecutionResult = _reflection.GeneratedProtocolMessageType('VodListWorkflowExecutionResult', (_message.Message,), {
  'DESCRIPTOR' : _VODLISTWORKFLOWEXECUTIONRESULT,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.VodListWorkflowExecutionResult)
  })
_sym_db.RegisterMessage(VodListWorkflowExecutionResult)

WorkflowExecution = _reflection.GeneratedProtocolMessageType('WorkflowExecution', (_message.Message,), {
  'DESCRIPTOR' : _WORKFLOWEXECUTION,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.WorkflowExecution)
  })
_sym_db.RegisterMessage(WorkflowExecution)

VodGetWorkflowExecutionDetailResult = _reflection.GeneratedProtocolMessageType('VodGetWorkflowExecutionDetailResult', (_message.Message,), {
  'DESCRIPTOR' : _VODGETWORKFLOWEXECUTIONDETAILRESULT,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.VodGetWorkflowExecutionDetailResult)
  })
_sym_db.RegisterMessage(VodGetWorkflowExecutionDetailResult)

ExecutionStage = _reflection.GeneratedProtocolMessageType('ExecutionStage', (_message.Message,), {
  'DESCRIPTOR' : _EXECUTIONSTAGE,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.ExecutionStage)
  })
_sym_db.RegisterMessage(ExecutionStage)

StageDetail = _reflection.GeneratedProtocolMessageType('StageDetail', (_message.Message,), {
  'DESCRIPTOR' : _STAGEDETAIL,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.StageDetail)
  })
_sym_db.RegisterMessage(StageDetail)

TaskDetail = _reflection.GeneratedProtocolMessageType('TaskDetail', (_message.Message,), {
  'DESCRIPTOR' : _TASKDETAIL,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.TaskDetail)
  })
_sym_db.RegisterMessage(TaskDetail)

SnapshotParamsPoster = _reflection.GeneratedProtocolMessageType('SnapshotParamsPoster', (_message.Message,), {
  'DESCRIPTOR' : _SNAPSHOTPARAMSPOSTER,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.SnapshotParamsPoster)
  })
_sym_db.RegisterMessage(SnapshotParamsPoster)

SnapshotParamsDynpost = _reflection.GeneratedProtocolMessageType('SnapshotParamsDynpost', (_message.Message,), {
  'DESCRIPTOR' : _SNAPSHOTPARAMSDYNPOST,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.SnapshotParamsDynpost)
  })
_sym_db.RegisterMessage(SnapshotParamsDynpost)

SnapshotParamsAIDynpost = _reflection.GeneratedProtocolMessageType('SnapshotParamsAIDynpost', (_message.Message,), {
  'DESCRIPTOR' : _SNAPSHOTPARAMSAIDYNPOST,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.SnapshotParamsAIDynpost)
  })
_sym_db.RegisterMessage(SnapshotParamsAIDynpost)

SnapshotParamsAnimatedPoster = _reflection.GeneratedProtocolMessageType('SnapshotParamsAnimatedPoster', (_message.Message,), {
  'DESCRIPTOR' : _SNAPSHOTPARAMSANIMATEDPOSTER,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.SnapshotParamsAnimatedPoster)
  })
_sym_db.RegisterMessage(SnapshotParamsAnimatedPoster)

SnapshotParamsSprite = _reflection.GeneratedProtocolMessageType('SnapshotParamsSprite', (_message.Message,), {
  'DESCRIPTOR' : _SNAPSHOTPARAMSSPRITE,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.SnapshotParamsSprite)
  })
_sym_db.RegisterMessage(SnapshotParamsSprite)

SnapshotParamsSample = _reflection.GeneratedProtocolMessageType('SnapshotParamsSample', (_message.Message,), {
  'DESCRIPTOR' : _SNAPSHOTPARAMSSAMPLE,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.SnapshotParamsSample)
  })
_sym_db.RegisterMessage(SnapshotParamsSample)

SnapshotResult = _reflection.GeneratedProtocolMessageType('SnapshotResult', (_message.Message,), {
  'DESCRIPTOR' : _SNAPSHOTRESULT,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.SnapshotResult)
  })
_sym_db.RegisterMessage(SnapshotResult)

VodWorkflowResult = _reflection.GeneratedProtocolMessageType('VodWorkflowResult', (_message.Message,), {
  'DESCRIPTOR' : _VODWORKFLOWRESULT,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.VodWorkflowResult)
  })
_sym_db.RegisterMessage(VodWorkflowResult)

TaskTemplate = _reflection.GeneratedProtocolMessageType('TaskTemplate', (_message.Message,), {
  'DESCRIPTOR' : _TASKTEMPLATE,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.TaskTemplate)
  })
_sym_db.RegisterMessage(TaskTemplate)

TranscodeVideoTaskParams = _reflection.GeneratedProtocolMessageType('TranscodeVideoTaskParams', (_message.Message,), {
  'DESCRIPTOR' : _TRANSCODEVIDEOTASKPARAMS,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.TranscodeVideoTaskParams)
  })
_sym_db.RegisterMessage(TranscodeVideoTaskParams)

ByteHDTaskParams = _reflection.GeneratedProtocolMessageType('ByteHDTaskParams', (_message.Message,), {
  'DESCRIPTOR' : _BYTEHDTASKPARAMS,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.ByteHDTaskParams)
  })
_sym_db.RegisterMessage(ByteHDTaskParams)

TranscodeAudioTaskParams = _reflection.GeneratedProtocolMessageType('TranscodeAudioTaskParams', (_message.Message,), {
  'DESCRIPTOR' : _TRANSCODEAUDIOTASKPARAMS,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.TranscodeAudioTaskParams)
  })
_sym_db.RegisterMessage(TranscodeAudioTaskParams)

SnapshotTaskParams = _reflection.GeneratedProtocolMessageType('SnapshotTaskParams', (_message.Message,), {
  'DESCRIPTOR' : _SNAPSHOTTASKPARAMS,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.SnapshotTaskParams)
  })
_sym_db.RegisterMessage(SnapshotTaskParams)

PosterSnapshotParams = _reflection.GeneratedProtocolMessageType('PosterSnapshotParams', (_message.Message,), {
  'DESCRIPTOR' : _POSTERSNAPSHOTPARAMS,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.PosterSnapshotParams)
  })
_sym_db.RegisterMessage(PosterSnapshotParams)

DynpostSnapshotParams = _reflection.GeneratedProtocolMessageType('DynpostSnapshotParams', (_message.Message,), {
  'DESCRIPTOR' : _DYNPOSTSNAPSHOTPARAMS,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.DynpostSnapshotParams)
  })
_sym_db.RegisterMessage(DynpostSnapshotParams)

AnimatedPosterSnapshotParams = _reflection.GeneratedProtocolMessageType('AnimatedPosterSnapshotParams', (_message.Message,), {
  'DESCRIPTOR' : _ANIMATEDPOSTERSNAPSHOTPARAMS,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.AnimatedPosterSnapshotParams)
  })
_sym_db.RegisterMessage(AnimatedPosterSnapshotParams)

SpriteSnapshotParams = _reflection.GeneratedProtocolMessageType('SpriteSnapshotParams', (_message.Message,), {
  'DESCRIPTOR' : _SPRITESNAPSHOTPARAMS,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.SpriteSnapshotParams)
  })
_sym_db.RegisterMessage(SpriteSnapshotParams)

SampleSnapshotParams = _reflection.GeneratedProtocolMessageType('SampleSnapshotParams', (_message.Message,), {
  'DESCRIPTOR' : _SAMPLESNAPSHOTPARAMS,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.SampleSnapshotParams)
  })
_sym_db.RegisterMessage(SampleSnapshotParams)

VodTaskTemplateResult = _reflection.GeneratedProtocolMessageType('VodTaskTemplateResult', (_message.Message,), {
  'DESCRIPTOR' : _VODTASKTEMPLATERESULT,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.VodTaskTemplateResult)
  })
_sym_db.RegisterMessage(VodTaskTemplateResult)

VodListTaskTemplateResult = _reflection.GeneratedProtocolMessageType('VodListTaskTemplateResult', (_message.Message,), {
  'DESCRIPTOR' : _VODLISTTASKTEMPLATERESULT,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.VodListTaskTemplateResult)
  })
_sym_db.RegisterMessage(VodListTaskTemplateResult)

WorkflowTemplate = _reflection.GeneratedProtocolMessageType('WorkflowTemplate', (_message.Message,), {
  'DESCRIPTOR' : _WORKFLOWTEMPLATE,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.WorkflowTemplate)
  })
_sym_db.RegisterMessage(WorkflowTemplate)

Activity = _reflection.GeneratedProtocolMessageType('Activity', (_message.Message,), {
  'DESCRIPTOR' : _ACTIVITY,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.Activity)
  })
_sym_db.RegisterMessage(Activity)

TranscodeActivity = _reflection.GeneratedProtocolMessageType('TranscodeActivity', (_message.Message,), {

  'EnhanceParams' : _reflection.GeneratedProtocolMessageType('EnhanceParams', (_message.Message,), {
    'DESCRIPTOR' : _TRANSCODEACTIVITY_ENHANCEPARAMS,
    '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
    # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.TranscodeActivity.EnhanceParams)
    })
  ,

  'LogoParams' : _reflection.GeneratedProtocolMessageType('LogoParams', (_message.Message,), {
    'DESCRIPTOR' : _TRANSCODEACTIVITY_LOGOPARAMS,
    '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
    # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.TranscodeActivity.LogoParams)
    })
  ,

  'SubtitleParams' : _reflection.GeneratedProtocolMessageType('SubtitleParams', (_message.Message,), {
    'DESCRIPTOR' : _TRANSCODEACTIVITY_SUBTITLEPARAMS,
    '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
    # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.TranscodeActivity.SubtitleParams)
    })
  ,
  'DESCRIPTOR' : _TRANSCODEACTIVITY,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.TranscodeActivity)
  })
_sym_db.RegisterMessage(TranscodeActivity)
_sym_db.RegisterMessage(TranscodeActivity.EnhanceParams)
_sym_db.RegisterMessage(TranscodeActivity.LogoParams)
_sym_db.RegisterMessage(TranscodeActivity.SubtitleParams)

SnapshotActivity = _reflection.GeneratedProtocolMessageType('SnapshotActivity', (_message.Message,), {
  'DESCRIPTOR' : _SNAPSHOTACTIVITY,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.SnapshotActivity)
  })
_sym_db.RegisterMessage(SnapshotActivity)

EndActivity = _reflection.GeneratedProtocolMessageType('EndActivity', (_message.Message,), {
  'DESCRIPTOR' : _ENDACTIVITY,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.EndActivity)
  })
_sym_db.RegisterMessage(EndActivity)

VodWorkflowTemplateResult = _reflection.GeneratedProtocolMessageType('VodWorkflowTemplateResult', (_message.Message,), {
  'DESCRIPTOR' : _VODWORKFLOWTEMPLATERESULT,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.VodWorkflowTemplateResult)
  })
_sym_db.RegisterMessage(VodWorkflowTemplateResult)

VodListWorkflowTemplateResult = _reflection.GeneratedProtocolMessageType('VodListWorkflowTemplateResult', (_message.Message,), {
  'DESCRIPTOR' : _VODLISTWORKFLOWTEMPLATERESULT,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.VodListWorkflowTemplateResult)
  })
_sym_db.RegisterMessage(VodListWorkflowTemplateResult)

LogoTemplate = _reflection.GeneratedProtocolMessageType('LogoTemplate', (_message.Message,), {
  'DESCRIPTOR' : _LOGOTEMPLATE,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.LogoTemplate)
  })
_sym_db.RegisterMessage(LogoTemplate)

AdaptLogo = _reflection.GeneratedProtocolMessageType('AdaptLogo', (_message.Message,), {
  'DESCRIPTOR' : _ADAPTLOGO,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.AdaptLogo)
  })
_sym_db.RegisterMessage(AdaptLogo)

AnchorSize = _reflection.GeneratedProtocolMessageType('AnchorSize', (_message.Message,), {
  'DESCRIPTOR' : _ANCHORSIZE,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.AnchorSize)
  })
_sym_db.RegisterMessage(AnchorSize)

ImageLogoDefinition = _reflection.GeneratedProtocolMessageType('ImageLogoDefinition', (_message.Message,), {
  'DESCRIPTOR' : _IMAGELOGODEFINITION,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.ImageLogoDefinition)
  })
_sym_db.RegisterMessage(ImageLogoDefinition)

VideoLogoDefinition = _reflection.GeneratedProtocolMessageType('VideoLogoDefinition', (_message.Message,), {
  'DESCRIPTOR' : _VIDEOLOGODEFINITION,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.VideoLogoDefinition)
  })
_sym_db.RegisterMessage(VideoLogoDefinition)

TextLogoDefinition = _reflection.GeneratedProtocolMessageType('TextLogoDefinition', (_message.Message,), {
  'DESCRIPTOR' : _TEXTLOGODEFINITION,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.TextLogoDefinition)
  })
_sym_db.RegisterMessage(TextLogoDefinition)

LogoDefinition = _reflection.GeneratedProtocolMessageType('LogoDefinition', (_message.Message,), {
  'DESCRIPTOR' : _LOGODEFINITION,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.LogoDefinition)
  })
_sym_db.RegisterMessage(LogoDefinition)

FontShadow = _reflection.GeneratedProtocolMessageType('FontShadow', (_message.Message,), {
  'DESCRIPTOR' : _FONTSHADOW,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.FontShadow)
  })
_sym_db.RegisterMessage(FontShadow)

AdaptConcat = _reflection.GeneratedProtocolMessageType('AdaptConcat', (_message.Message,), {
  'DESCRIPTOR' : _ADAPTCONCAT,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.AdaptConcat)
  })
_sym_db.RegisterMessage(AdaptConcat)

ConcatDefinition = _reflection.GeneratedProtocolMessageType('ConcatDefinition', (_message.Message,), {
  'DESCRIPTOR' : _CONCATDEFINITION,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.ConcatDefinition)
  })
_sym_db.RegisterMessage(ConcatDefinition)

HiddenWatermarkAdd = _reflection.GeneratedProtocolMessageType('HiddenWatermarkAdd', (_message.Message,), {
  'DESCRIPTOR' : _HIDDENWATERMARKADD,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.HiddenWatermarkAdd)
  })
_sym_db.RegisterMessage(HiddenWatermarkAdd)

VodListWatermarkResponseResult = _reflection.GeneratedProtocolMessageType('VodListWatermarkResponseResult', (_message.Message,), {
  'DESCRIPTOR' : _VODLISTWATERMARKRESPONSERESULT,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.VodListWatermarkResponseResult)
  })
_sym_db.RegisterMessage(VodListWatermarkResponseResult)

Video = _reflection.GeneratedProtocolMessageType('Video', (_message.Message,), {
  'DESCRIPTOR' : _VIDEO,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.Video)
  })
_sym_db.RegisterMessage(Video)

Audio = _reflection.GeneratedProtocolMessageType('Audio', (_message.Message,), {
  'DESCRIPTOR' : _AUDIO,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.Audio)
  })
_sym_db.RegisterMessage(Audio)

Segment = _reflection.GeneratedProtocolMessageType('Segment', (_message.Message,), {
  'DESCRIPTOR' : _SEGMENT,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.Segment)
  })
_sym_db.RegisterMessage(Segment)

Condition = _reflection.GeneratedProtocolMessageType('Condition', (_message.Message,), {
  'DESCRIPTOR' : _CONDITION,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.Condition)
  })
_sym_db.RegisterMessage(Condition)

ParallelParams = _reflection.GeneratedProtocolMessageType('ParallelParams', (_message.Message,), {
  'DESCRIPTOR' : _PARALLELPARAMS,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.ParallelParams)
  })
_sym_db.RegisterMessage(ParallelParams)

Encryption = _reflection.GeneratedProtocolMessageType('Encryption', (_message.Message,), {
  'DESCRIPTOR' : _ENCRYPTION,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.Encryption)
  })
_sym_db.RegisterMessage(Encryption)

Volume = _reflection.GeneratedProtocolMessageType('Volume', (_message.Message,), {
  'DESCRIPTOR' : _VOLUME,
  '__module__' : 'volcengine.vod.business.vod_workflow_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.Volume)
  })
_sym_db.RegisterMessage(Volume)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n)com.volcengine.service.vod.model.businessB\013VodWorkflowP\001ZAgithub.com/volcengine/volc-sdk-golang/service/vod/models/business\240\001\001\330\001\001\312\002 Volc\\Service\\Vod\\Models\\Business\342\002#Volc\\Service\\Vod\\Models\\GPBMetadata'
  _WORKFLOWPARAMS_CONDITIONENTRY._options = None
  _WORKFLOWPARAMS_CONDITIONENTRY._serialized_options = b'8\001'
  _LOGOOVERRIDE_VARSENTRY._options = None
  _LOGOOVERRIDE_VARSENTRY._serialized_options = b'8\001'
  _CATEGORYTAGINFO_PARENTINFOENTRY._options = None
  _CATEGORYTAGINFO_PARENTINFOENTRY._serialized_options = b'8\001'
  _STAGESTATUS._serialized_start=14189
  _STAGESTATUS._serialized_end=14311
  _VODSTARTWORKFLOWRESULT._serialized_start=153
  _VODSTARTWORKFLOWRESULT._serialized_end=192
  _DIRECTURL._serialized_start=194
  _DIRECTURL._serialized_end=262
  _WORKFLOWPARAMS._serialized_start=265
  _WORKFLOWPARAMS._serialized_end=485
  _WORKFLOWPARAMS_CONDITIONENTRY._serialized_start=437
  _WORKFLOWPARAMS_CONDITIONENTRY._serialized_end=485
  _OVERRIDEPARAMS._serialized_start=488
  _OVERRIDEPARAMS._serialized_end=858
  _LOGOOVERRIDE._serialized_start=861
  _LOGOOVERRIDE._serialized_end=1010
  _LOGOOVERRIDE_VARSENTRY._serialized_start=967
  _LOGOOVERRIDE_VARSENTRY._serialized_end=1010
  _TRANSCODEVIDEOOVERRIDE._serialized_start=1013
  _TRANSCODEVIDEOOVERRIDE._serialized_end=1172
  _CLIP._serialized_start=1174
  _CLIP._serialized_end=1216
  _TRANSCODEAUDIOOVERRIDE._serialized_start=1218
  _TRANSCODEAUDIOOVERRIDE._serialized_end=1332
  _SNAPSHOTOVERRIDE._serialized_start=1335
  _SNAPSHOTOVERRIDE._serialized_end=1539
  _ENHANCEOVERRIDE._serialized_start=1541
  _ENHANCEOVERRIDE._serialized_end=1597
  _TRANSCODERESULT._serialized_start=1600
  _TRANSCODERESULT._serialized_end=1765
  _INSPECTION._serialized_start=1768
  _INSPECTION._serialized_end=1898
  _QUALITY._serialized_start=1901
  _QUALITY._serialized_end=2037
  _DELOGOINFO._serialized_start=2039
  _DELOGOINFO._serialized_end=2152
  _VISUALQUALITY._serialized_start=2154
  _VISUALQUALITY._serialized_end=2278
  _VOLUMEINFO._serialized_start=2280
  _VOLUMEINFO._serialized_end=2363
  _CATEGORYTAGINFO._serialized_start=2366
  _CATEGORYTAGINFO._serialized_end=2580
  _CATEGORYTAGINFO_PARENTINFOENTRY._serialized_start=2531
  _CATEGORYTAGINFO_PARENTINFOENTRY._serialized_end=2580
  _VODLISTWORKFLOWEXECUTIONRESULT._serialized_start=2583
  _VODLISTWORKFLOWEXECUTIONRESULT._serialized_end=2734
  _WORKFLOWEXECUTION._serialized_start=2737
  _WORKFLOWEXECUTION._serialized_end=3297
  _VODGETWORKFLOWEXECUTIONDETAILRESULT._serialized_start=3300
  _VODGETWORKFLOWEXECUTIONDETAILRESULT._serialized_end=3752
  _EXECUTIONSTAGE._serialized_start=3755
  _EXECUTIONSTAGE._serialized_end=3950
  _STAGEDETAIL._serialized_start=3953
  _STAGEDETAIL._serialized_end=4222
  _TASKDETAIL._serialized_start=4225
  _TASKDETAIL._serialized_end=4449
  _SNAPSHOTPARAMSPOSTER._serialized_start=4451
  _SNAPSHOTPARAMSPOSTER._serialized_end=4538
  _SNAPSHOTPARAMSDYNPOST._serialized_start=4540
  _SNAPSHOTPARAMSDYNPOST._serialized_end=4628
  _SNAPSHOTPARAMSAIDYNPOST._serialized_start=4630
  _SNAPSHOTPARAMSAIDYNPOST._serialized_end=4720
  _SNAPSHOTPARAMSANIMATEDPOSTER._serialized_start=4722
  _SNAPSHOTPARAMSANIMATEDPOSTER._serialized_end=4817
  _SNAPSHOTPARAMSSPRITE._serialized_start=4820
  _SNAPSHOTPARAMSSPRITE._serialized_end=4988
  _SNAPSHOTPARAMSSAMPLE._serialized_start=4991
  _SNAPSHOTPARAMSSAMPLE._serialized_end=5170
  _SNAPSHOTRESULT._serialized_start=5173
  _SNAPSHOTRESULT._serialized_end=5677
  _VODWORKFLOWRESULT._serialized_start=5680
  _VODWORKFLOWRESULT._serialized_end=6028
  _TASKTEMPLATE._serialized_start=6031
  _TASKTEMPLATE._serialized_end=6617
  _TRANSCODEVIDEOTASKPARAMS._serialized_start=6620
  _TRANSCODEVIDEOTASKPARAMS._serialized_end=7072
  _BYTEHDTASKPARAMS._serialized_start=7075
  _BYTEHDTASKPARAMS._serialized_end=7488
  _TRANSCODEAUDIOTASKPARAMS._serialized_start=7491
  _TRANSCODEAUDIOTASKPARAMS._serialized_end=7819
  _SNAPSHOTTASKPARAMS._serialized_start=7822
  _SNAPSHOTTASKPARAMS._serialized_end=8294
  _POSTERSNAPSHOTPARAMS._serialized_start=8297
  _POSTERSNAPSHOTPARAMS._serialized_end=8440
  _DYNPOSTSNAPSHOTPARAMS._serialized_start=8443
  _DYNPOSTSNAPSHOTPARAMS._serialized_end=8640
  _ANIMATEDPOSTERSNAPSHOTPARAMS._serialized_start=8643
  _ANIMATEDPOSTERSNAPSHOTPARAMS._serialized_end=8834
  _SPRITESNAPSHOTPARAMS._serialized_start=8837
  _SPRITESNAPSHOTPARAMS._serialized_end=9060
  _SAMPLESNAPSHOTPARAMS._serialized_start=9063
  _SAMPLESNAPSHOTPARAMS._serialized_end=9279
  _VODTASKTEMPLATERESULT._serialized_start=9281
  _VODTASKTEMPLATERESULT._serialized_end=9372
  _VODLISTTASKTEMPLATERESULT._serialized_start=9375
  _VODLISTTASKTEMPLATERESULT._serialized_end=9508
  _WORKFLOWTEMPLATE._serialized_start=9511
  _WORKFLOWTEMPLATE._serialized_end=9791
  _ACTIVITY._serialized_start=9794
  _ACTIVITY._serialized_end=10093
  _TRANSCODEACTIVITY._serialized_start=10096
  _TRANSCODEACTIVITY._serialized_end=10581
  _TRANSCODEACTIVITY_ENHANCEPARAMS._serialized_start=10441
  _TRANSCODEACTIVITY_ENHANCEPARAMS._serialized_end=10493
  _TRANSCODEACTIVITY_LOGOPARAMS._serialized_start=10495
  _TRANSCODEACTIVITY_LOGOPARAMS._serialized_end=10527
  _TRANSCODEACTIVITY_SUBTITLEPARAMS._serialized_start=10529
  _TRANSCODEACTIVITY_SUBTITLEPARAMS._serialized_end=10581
  _SNAPSHOTACTIVITY._serialized_start=10583
  _SNAPSHOTACTIVITY._serialized_end=10701
  _ENDACTIVITY._serialized_start=10703
  _ENDACTIVITY._serialized_end=10740
  _VODWORKFLOWTEMPLATERESULT._serialized_start=10742
  _VODWORKFLOWTEMPLATERESULT._serialized_end=10845
  _VODLISTWORKFLOWTEMPLATERESULT._serialized_start=10848
  _VODLISTWORKFLOWTEMPLATERESULT._serialized_end=10989
  _LOGOTEMPLATE._serialized_start=10992
  _LOGOTEMPLATE._serialized_end=11417
  _ADAPTLOGO._serialized_start=11420
  _ADAPTLOGO._serialized_end=11558
  _ANCHORSIZE._serialized_start=11560
  _ANCHORSIZE._serialized_end=11603
  _IMAGELOGODEFINITION._serialized_start=11606
  _IMAGELOGODEFINITION._serialized_end=11889
  _VIDEOLOGODEFINITION._serialized_start=11892
  _VIDEOLOGODEFINITION._serialized_end=12175
  _TEXTLOGODEFINITION._serialized_start=12178
  _TEXTLOGODEFINITION._serialized_end=12458
  _LOGODEFINITION._serialized_start=12461
  _LOGODEFINITION._serialized_end=12735
  _FONTSHADOW._serialized_start=12737
  _FONTSHADOW._serialized_end=12798
  _ADAPTCONCAT._serialized_start=12801
  _ADAPTCONCAT._serialized_end=12945
  _CONCATDEFINITION._serialized_start=12947
  _CONCATDEFINITION._serialized_end=13010
  _HIDDENWATERMARKADD._serialized_start=13012
  _HIDDENWATERMARKADD._serialized_end=13063
  _VODLISTWATERMARKRESPONSERESULT._serialized_start=13066
  _VODLISTWATERMARKRESPONSERESULT._serialized_end=13204
  _VIDEO._serialized_start=13207
  _VIDEO._serialized_end=13536
  _AUDIO._serialized_start=13539
  _AUDIO._serialized_end=13692
  _SEGMENT._serialized_start=13694
  _SEGMENT._serialized_end=13751
  _CONDITION._serialized_start=13754
  _CONDITION._serialized_end=14014
  _PARALLELPARAMS._serialized_start=14016
  _PARALLELPARAMS._serialized_end=14049
  _ENCRYPTION._serialized_start=14051
  _ENCRYPTION._serialized_end=14079
  _VOLUME._serialized_start=14081
  _VOLUME._serialized_end=14187
# @@protoc_insertion_point(module_scope)
