# Code generated by protoc-gen-volcengine-sdk
# source: ImpService
# DO NOT EDIT!
# coding:utf-8
from __future__ import print_function
from volcengine.Policy import *
from google.protobuf.json_format import *
from volcengine.imp.ImpServiceConfig import ImpServiceConfig
from retry import retry
from zlib import crc32
import os
import sys
import time
import datetime
from volcengine.util.Util import Util
from volcengine.imp.models.request.request_imp_pb2 import *
from volcengine.imp.models.response.response_imp_pb2 import *

MinChunkSize = 1024 * 1024 * 20
LargeFileSize = 1024 * 1024 * 1024


#
# Generated from protobuf service <code>ImpService</code>
#
class ImpService(ImpServiceConfig):

    #
    # SubmitJob.
    #
    # @param request ImpSubmitJobRequest
    # @return ImpSubmitJobResponse
    # @raise Exception
    def submit_job(self, request):
        try:
            if sys.version_info[0] == 3:
                jsonData = MessageToJson(request, False, True)
                params = json.loads(jsonData)
                for k, v in params.items():
                    if isinstance(v, (int, float, bool, str)) is True:
                        continue
                    else:
                        params[k] = json.dumps(v)
            else:
                params = MessageToDict(request, False, True)
                for k, v in params.items():
                    if isinstance(v, (int, float, bool, str, unicode)) is True:
                        continue
                    else:
                        params[k] = json.dumps(v)
            res = self.get("SubmitJob", params)
        except Exception as Argument:
            try:
                resp = Parse(Argument.__str__(), ImpSubmitJobResponse(), True)
            except Exception:
                raise Argument
            else:
                raise Exception(resp.ResponseMetadata.Error.Code)
        else:
            return Parse(res, ImpSubmitJobResponse(), True)

    #
    # KillJob.
    #
    # @param request ImpKillJobRequest
    # @return ImpKillJobResponse
    # @raise Exception
    def kill_job(self, request):
        try:
            if sys.version_info[0] == 3:
                jsonData = MessageToJson(request, False, True)
                params = json.loads(jsonData)
                for k, v in params.items():
                    if isinstance(v, (int, float, bool, str)) is True:
                        continue
                    else:
                        params[k] = json.dumps(v)
            else:
                params = MessageToDict(request, False, True)
                for k, v in params.items():
                    if isinstance(v, (int, float, bool, str, unicode)) is True:
                        continue
                    else:
                        params[k] = json.dumps(v)
            res = self.get("KillJob", params)
        except Exception as Argument:
            try:
                resp = Parse(Argument.__str__(), ImpKillJobResponse(), True)
            except Exception:
                raise Argument
            else:
                raise Exception(resp.ResponseMetadata.Error.Code)
        else:
            return Parse(res, ImpKillJobResponse(), True)

    #
    # RetrieveJob.
    #
    # @param request ImpRetrieveJobRequest
    # @return ImpRetrieveJobResponse
    # @raise Exception
    def retrieve_job(self, request):
        try:
            if sys.version_info[0] == 3:
                jsonData = MessageToJson(request, False, True)
                params = json.loads(jsonData)
                for k, v in params.items():
                    if isinstance(v, (int, float, bool, str)) is True:
                        continue
                    else:
                        params[k] = json.dumps(v)
            else:
                params = MessageToDict(request, False, True)
                for k, v in params.items():
                    if isinstance(v, (int, float, bool, str, unicode)) is True:
                        continue
                    else:
                        params[k] = json.dumps(v)
            res = self.get("RetrieveJob", params)
        except Exception as Argument:
            try:
                resp = Parse(Argument.__str__(), ImpRetrieveJobResponse(), True)
            except Exception:
                raise Argument
            else:
                raise Exception(resp.ResponseMetadata.Error.Code)
        else:
            return Parse(res, ImpRetrieveJobResponse(), True)

