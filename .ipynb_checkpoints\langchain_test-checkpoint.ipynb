{"cells": [{"cell_type": "code", "execution_count": 1, "id": "83aa9755", "metadata": {}, "outputs": [], "source": ["import os\n", "\n", "from langchain_openai import ChatOpenAI\n", "\n", "model = ChatOpenAI(model=\"kimi2\",\n", "                  api_key=\"tc-lPYsGhv3Q4m1BUXt8140EdE0129f44E58704141e0c41B589\",\n", "                  base_url=\"http://aiinone.seasungame.com:8000/ai_in_one/v2/chat/completions\")"]}, {"cell_type": "code", "execution_count": null, "id": "9e19b2e5", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": ".venv", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}