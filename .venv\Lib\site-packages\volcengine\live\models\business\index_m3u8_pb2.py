# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: live/business/index_m3u8.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1elive/business/index_m3u8.proto\x12\x1fVolcengine.Live.Models.Business\"\xfd\x01\n&CreateLiveStreamRecordIndexFilesResult\x12\x0e\n\x06\x44omain\x18\x01 \x01(\t\x12\x0b\n\x03\x41pp\x18\x02 \x01(\t\x12\x0e\n\x06Stream\x18\x03 \x01(\t\x12\x11\n\tStartTime\x18\x04 \x01(\t\x12\x0f\n\x07\x45ndTime\x18\x05 \x01(\t\x12\x14\n\x0cOutputBucket\x18\x06 \x01(\t\x12\x14\n\x0cOutputObject\x18\x07 \x01(\t\x12\x11\n\tRecordURL\x18\x08 \x01(\t\x12\x10\n\x08\x44uration\x18\t \x01(\x01\x12\x0e\n\x06Height\x18\n \x01(\x03\x12\r\n\x05Width\x18\x0b \x01(\x03\x12\x12\n\nCreateTime\x18\x0c \x01(\tB\xcc\x01\n*com.volcengine.service.live.model.businessB\x04\x41\x64\x64rP\x01ZBgithub.com/volcengine/volc-sdk-golang/service/live/models/business\xa0\x01\x01\xd8\x01\x01\xc2\x02\x00\xca\x02!Volc\\Service\\Live\\Models\\Business\xe2\x02$Volc\\Service\\Live\\Models\\GPBMetadatab\x06proto3')



_CREATELIVESTREAMRECORDINDEXFILESRESULT = DESCRIPTOR.message_types_by_name['CreateLiveStreamRecordIndexFilesResult']
CreateLiveStreamRecordIndexFilesResult = _reflection.GeneratedProtocolMessageType('CreateLiveStreamRecordIndexFilesResult', (_message.Message,), {
  'DESCRIPTOR' : _CREATELIVESTREAMRECORDINDEXFILESRESULT,
  '__module__' : 'live.business.index_m3u8_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Business.CreateLiveStreamRecordIndexFilesResult)
  })
_sym_db.RegisterMessage(CreateLiveStreamRecordIndexFilesResult)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n*com.volcengine.service.live.model.businessB\004AddrP\001ZBgithub.com/volcengine/volc-sdk-golang/service/live/models/business\240\001\001\330\001\001\302\002\000\312\002!Volc\\Service\\Live\\Models\\Business\342\002$Volc\\Service\\Live\\Models\\GPBMetadata'
  _CREATELIVESTREAMRECORDINDEXFILESRESULT._serialized_start=68
  _CREATELIVESTREAMRECORDINDEXFILESRESULT._serialized_end=321
# @@protoc_insertion_point(module_scope)
