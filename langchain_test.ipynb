import os
import getpass
import os
from langchain_openai import ChatOpenAI

llm = ChatOpenAI(model="kimi2",
                  api_key="tc-lPYsGhv3Q4m1BUXt8140EdE0129f44E58704141e0c41B589",
                  base_url="http://aiinone.seasungame.com:8000/ai_in_one/v2/chat/completions")
os.environ["LANGSMITH_TRACING"] = "true"
os.environ["LANGSMITH_API_KEY"] = "123"

from langchain_core.messages import HumanMessage

llm.invoke([HumanMessage(content="What's my name")])


from langchain_core.messages import AIMessage
llm.invoke(
    [
        HumanMessage(content="Hi! I'm Bob"),
        AIMessage(content="Hello Bob! How can I assist you today?"),
        HumanMessage(content="What's my name?"),
    ]
)


from langgraph.checkpoint.memory import MemorySaver
from langgraph.graph import StateGraph, MessagesState, START, END

workflow = StateGraph(state_schema=MessagesState)

def call_model(state:MessagesState):
    print(state)
    print(type(state))
    response = model.invoke(state["messages"])
    return {"messages": response}

workflow.add_edge(START, "model")
workflow.add_node("model", call_model)

memory = MemorySaver()
app = workflow.compile(checkpointer=memory)

config = {"configurable": {"thread_id": "abc123"}}

query = "Hi! I'm Bob."

input_messages = [HumanMessage(query)]
output = app.invoke({"messages": input_messages}, config)
output["messages"][-1].pretty_print()  # output contains all messages in state

from langchain_core.vectorstores import InMemoryVectorStore
from langchain_openai import OpenAIEmbeddings

embeddings = OpenAIEmbeddings(base_url="https://ark.cn-beijing.volces.com/api/v3",
                              model="ep-20250904104415-7vwrn",
                              api_key="ccbd6aba-4323-44a8-a4fb-71c5b28631b1",
                                )

vector_store = InMemoryVectorStore(embedding=embeddings)


import bs4
from langchain import hub
from langchain_community.document_loaders import WebBaseLoader
from langchain_core.documents import Document
from langchain_text_splitters import RecursiveCharacterTextSplitter
from langgraph.graph import START, StateGraph
from typing_extensions import List, TypedDict

# Load and chunk contents of the blog
loader = WebBaseLoader(
    web_paths=("https://lilianweng.github.io/posts/2023-06-23-agent/",),
    bs_kwargs=dict(
        parse_only=bs4.SoupStrainer(
            class_=("post-content", "post-title", "post-header")
        )
    ),
)

docs = loader.load()
text_splitter = RecursiveCharacterTextSplitter(chunk_size=500, chunk_overlap=200)
all_splits = text_splitter.split_documents(docs)
_ = vector_store.add_documents(documents=all_splits)

