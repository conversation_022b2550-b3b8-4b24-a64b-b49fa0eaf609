from volcengine.ApiInfo import ApiInfo
from volcengine.imagex.ImageXConfig import api_info, IMAGEX_API_VERSION


def init():
    for v in ["DescribeImageXDomainTrafficData",
              "DescribeImageXDomainBandwidthData",
              "DescribeImageXBucketUsage",
              "DescribeImageXRequestCntUsage",
              "DescribeImageXBaseOpUsage",
              "DescribeImageXCompressUsage",
              "DescribeImageXEdgeRequest",
              "DescribeImageXHitRateTrafficData",
              "DescribeImageXHitRateRequestData",
              "DescribeImageXCDNTopRequestData",
              "DescribeImageXSummary",
              "DescribeImageXEdgeRequestBandwidth",
              "DescribeImageXEdgeRequestTraffic",
              "DescribeImageXEdgeRequestRegions",
              "DescribeImageXServiceQuality",
              "GetImageXQueryApps",
              "GetImageXQueryRegions",
              "GetImageXQueryDims",
              "GetImageXQueryVals",
              ]:
        api_info[v] = ApiInfo("GET", "/", {"Action": v, "Version": IMAGEX_API_VERSION}, {}, {})

    for v in ["DescribeImageXMirrorRequestTraffic",
              "DescribeImageXMirrorRequestBandwidth",
              "DescribeImageXMirrorRequestHttpCodeByTime",
              "DescribeImageXMirrorRequestHttpCodeOverview",
              "DescribeImageXUploadSuccessRateByTime",
              "DescribeImageXUploadErrorCodeAll",
              "DescribeImageXUploadErrorCodeByTime",
              "DescribeImageXUploadCountByTime",
              "DescribeImageXUploadFileSize",
              "DescribeImageXUploadSpeed",
              "DescribeImageXUploadDuration",
              "DescribeImageXUploadSegmentSpeedByTime",
              "DescribeImageXCdnSuccessRateByTime",
              "DescribeImageXCdnSuccessRateAll",
              "DescribeImageXCdnErrorCodeByTime",
              "DescribeImageXCdnErrorCodeAll",
              "DescribeImageXCdnDurationDetailByTime",
              "DescribeImageXCdnDurationAll",
              "DescribeImageXCdnReuseRateByTime",
              "DescribeImageXCdnReuseRateAll",
              "DescribeImageXCdnProtocolRateByTime",
              "DescribeImageXClientErrorCodeAll",
              "DescribeImageXClientErrorCodeByTime",
              "DescribeImageXClientDecodeSuccessRateByTime",
              "DescribeImageXClientDecodeDurationByTime",
              "DescribeImageXClientQueueDurationByTime",
              "DescribeImageXClientLoadDurationAll",
              "DescribeImageXClientLoadDuration",
              "DescribeImageXClientFailureRate",
              "DescribeImageXClientSdkVerByTime",
              "DescribeImageXClientFileSize",
              "DescribeImageXClientTopFileSize",
              "DescribeImageXClientCountByTime",
              "DescribeImageXClientScoreByTime",
              "DescribeImageXClientDemotionRateByTime",
              "DescribeImageXClientTopDemotionURL",
              "DescribeImageXClientQualityRateByTime",
              "DescribeImageXClientTopQualityURL",
              "DescribeImageXSensibleCountByTime",
              "DescribeImageXSensibleCacheHitRateByTime",
              "DescribeImageXSensibleTopSizeURL",
              "DescribeImageXSensibleTopRamURL",
              "DescribeImageXSensibleTopResolutionURL",
              "DescribeImageXSensibleTopUnknownURL",
              ]:
        api_info[v] = ApiInfo("POST", "/", {"Action": v, "Version": IMAGEX_API_VERSION}, {}, {})


init()
