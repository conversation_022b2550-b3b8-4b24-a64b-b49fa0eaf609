# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: volcengine/vod/business/vod_upload.proto
"""Generated protocol buffer code."""
from google.protobuf.internal import enum_type_wrapper
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from volcengine.vod.models.business import vod_common_pb2 as volcengine_dot_vod_dot_business_dot_vod__common__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n(volcengine/vod/business/vod_upload.proto\x12\x1eVolcengine.Vod.Models.Business\x1a(volcengine/vod/business/vod_common.proto\"\xad\x05\n\x12VodUrlUploadURLSet\x12\x11\n\tSourceUrl\x18\x01 \x01(\t\x12\x14\n\x0c\x43\x61llbackArgs\x18\x02 \x01(\t\x12\x0b\n\x03Md5\x18\x03 \x01(\t\x12\x12\n\nTemplateId\x18\x04 \x01(\t\x12\r\n\x05Title\x18\x05 \x01(\t\x12\x13\n\x0b\x44\x65scription\x18\x06 \x01(\t\x12\x0c\n\x04Tags\x18\x07 \x01(\t\x12\x10\n\x08\x43\x61tegory\x18\x08 \x01(\t\x12\x10\n\x08\x46ileName\x18\t \x01(\t\x12\x18\n\x10\x43lassificationId\x18\n \x01(\x03\x12\x14\n\x0cStorageClass\x18\x0b \x01(\x05\x12\x15\n\rFileExtension\x18\x0c \x01(\t\x12\x1e\n\x16UrlEncryptionAlgorithm\x18\r \x01(\t\x12\x19\n\x11\x45nableLowPriority\x18\x0e \x01(\x08\x12\x62\n\x10\x43ustomURLHeaders\x18\x0f \x03(\x0b\x32H.Volcengine.Vod.Models.Business.VodUrlUploadURLSet.CustomURLHeadersEntry\x12\x44\n\tTemplates\x18\x10 \x03(\x0b\x32\x31.Volcengine.Vod.Models.Business.VodUploadTemplate\x12\x10\n\x08\x46ileType\x18\x11 \x01(\t\x12>\n\x08ImageSet\x18\x12 \x03(\x0b\x32,.Volcengine.Vod.Models.Business.VodImageFile\x12@\n\nExecutions\x18\x13 \x03(\x0b\x32,.Volcengine.Vod.Models.Business.VodExecution\x1a\x37\n\x15\x43ustomURLHeadersEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"-\n\x0cVodImageFile\x12\x0b\n\x03Url\x18\x01 \x01(\t\x12\x10\n\x08\x46ileName\x18\x02 \x01(\t\"\x9e\x01\n\x0cVodExecution\x12H\n\tOperation\x18\x01 \x01(\x0b\x32\x35.Volcengine.Vod.Models.Business.VodExecutionOperation\x12\x44\n\x07\x43ontrol\x18\x02 \x01(\x0b\x32\x33.Volcengine.Vod.Models.Business.VodExecutionControl\"n\n\x15VodExecutionOperation\x12\x0c\n\x04Type\x18\x01 \x01(\t\x12G\n\x04Task\x18\x02 \x01(\x0b\x32\x39.Volcengine.Vod.Models.Business.VodExecutionOperationTask\"\xbf\x01\n\x19VodExecutionOperationTask\x12\x0c\n\x04Type\x18\x01 \x01(\t\x12I\n\x03Ocr\x18\x02 \x01(\x0b\x32<.Volcengine.Vod.Models.Business.VodExecutionOperationTaskOcr\x12I\n\x03\x41sr\x18\x03 \x01(\x0b\x32<.Volcengine.Vod.Models.Business.VodExecutionOperationTaskAsr\"B\n\x1cVodExecutionOperationTaskOcr\x12\x14\n\x0cWithImageSet\x18\x01 \x01(\x08\x12\x0c\n\x04Mode\x18\x02 \x01(\t\"}\n\x1cVodExecutionOperationTaskAsr\x12\x0c\n\x04Type\x18\x01 \x01(\t\x12\x10\n\x08Language\x18\x02 \x01(\t\x12\x17\n\x0fWithSpeakerInfo\x18\x03 \x01(\t\x12\x16\n\x0eWithConfidence\x18\x04 \x01(\t\x12\x0c\n\x04Mode\x18\x05 \x01(\t\"@\n\x13VodExecutionControl\x12\x14\n\x0c\x43\x61llbackArgs\x18\x01 \x01(\t\x12\x13\n\x0b\x43lientToken\x18\x02 \x01(\t\"M\n\x12VodUrlResponseData\x12\x37\n\x04\x44\x61ta\x18\x01 \x03(\x0b\x32).Volcengine.Vod.Models.Business.ValuePair\"@\n\tValuePair\x12\r\n\x05JobId\x18\x01 \x01(\t\x12\x11\n\tSourceUrl\x18\x02 \x01(\t\x12\x11\n\tImageUrls\x18\x03 \x03(\t\"R\n\x0cVodQueryData\x12\x42\n\x04\x44\x61ta\x18\x01 \x01(\x0b\x32\x34.Volcengine.Vod.Models.Business.VodQueryUploadResult\"p\n\x14VodQueryUploadResult\x12@\n\rMediaInfoList\x18\x01 \x03(\x0b\x32).Volcengine.Vod.Models.Business.VodURLSet\x12\x16\n\x0eNotExistJobIds\x18\x02 \x03(\t\"^\n\rVodCommitData\x12M\n\x04\x44\x61ta\x18\x01 \x01(\x0b\x32?.Volcengine.Vod.Models.Business.VodCommitUploadInfoResponseData\"\xa7\x01\n\x1fVodCommitUploadInfoResponseData\x12\x0b\n\x03Vid\x18\x01 \x01(\t\x12\x41\n\nSourceInfo\x18\x02 \x01(\x0b\x32-.Volcengine.Vod.Models.Business.VodSourceInfo\x12\x11\n\tPosterUri\x18\x03 \x01(\t\x12\x14\n\x0c\x43\x61llbackArgs\x18\x04 \x01(\t\x12\x0b\n\x03Mid\x18\x05 \x01(\t\"\xdb\x01\n\tVodURLSet\x12\x11\n\tRequestId\x18\x01 \x01(\t\x12\r\n\x05JobId\x18\x02 \x01(\t\x12\x11\n\tSourceUrl\x18\x03 \x01(\t\x12\r\n\x05State\x18\x04 \x01(\t\x12\x0b\n\x03Vid\x18\x05 \x01(\t\x12\x11\n\tSpaceName\x18\x06 \x01(\t\x12\x11\n\tAccountId\x18\x07 \x01(\t\x12\x41\n\nSourceInfo\x18\x08 \x01(\x0b\x32-.Volcengine.Vod.Models.Business.VodSourceInfo\x12\x14\n\x0c\x43\x61llbackArgs\x18\t \x01(\t\"`\n\x18VodApplyUploadInfoResult\x12\x44\n\x04\x44\x61ta\x18\x01 \x01(\x0b\x32\x36.Volcengine.Vod.Models.Business.VodApplyUploadInfoData\"\x8f\x02\n\x16VodApplyUploadInfoData\x12G\n\rUploadAddress\x18\x01 \x01(\x0b\x32\x30.Volcengine.Vod.Models.Business.VodUploadAddress\x12Z\n\x18\x43\x61ndidateUploadAddresses\x18\x02 \x01(\x0b\x32\x38.Volcengine.Vod.Models.Business.CandidateUploadAddresses\x12P\n\x13VpcTosUploadAddress\x18\x03 \x01(\x0b\x32\x33.Volcengine.Vod.Models.Business.VpcTosUploadAddress\"\xc2\x01\n\x10VodUploadAddress\x12@\n\nStoreInfos\x18\x01 \x03(\x0b\x32,.Volcengine.Vod.Models.Business.VodStoreInfo\x12\x13\n\x0bUploadHosts\x18\x02 \x03(\t\x12\x43\n\x0cUploadHeader\x18\x03 \x03(\x0b\x32-.Volcengine.Vod.Models.Business.VodHeaderPair\x12\x12\n\nSessionKey\x18\x04 \x01(\t\"\x84\x02\n\x18\x43\x61ndidateUploadAddresses\x12J\n\x13MainUploadAddresses\x18\x01 \x03(\x0b\x32-.Volcengine.Vod.Models.Business.UploadAddress\x12L\n\x15\x42\x61\x63kupUploadAddresses\x18\x02 \x03(\x0b\x32-.Volcengine.Vod.Models.Business.UploadAddress\x12N\n\x17\x46\x61llbackUploadAddresses\x18\x03 \x03(\x0b\x32-.Volcengine.Vod.Models.Business.UploadAddress\".\n\x0cVodStoreInfo\x12\x10\n\x08StoreUri\x18\x01 \x01(\t\x12\x0c\n\x04\x41uth\x18\x02 \x01(\t\"+\n\rVodHeaderPair\x12\x0b\n\x03Key\x18\x01 \x01(\t\x12\r\n\x05Value\x18\x02 \x01(\t\"\xb1\x02\n\x13VpcTosUploadAddress\x12\x12\n\nUploadMode\x18\x01 \x01(\t\x12\x0e\n\x06PutUrl\x18\x02 \x01(\t\x12\x46\n\x0ePartUploadInfo\x18\x03 \x01(\x0b\x32..Volcengine.Vod.Models.Business.PartUploadInfo\x12]\n\rPutUrlHeaders\x18\x04 \x03(\x0b\x32\x46.Volcengine.Vod.Models.Business.VpcTosUploadAddress.PutUrlHeadersEntry\x12\x19\n\x11QuickCompleteMode\x18\x05 \x01(\t\x1a\x34\n\x12PutUrlHeadersEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"\xef\x01\n\x0ePartUploadInfo\x12\x10\n\x08PartSize\x18\x01 \x01(\x03\x12\x13\n\x0bPartPutUrls\x18\x02 \x03(\t\x12\x17\n\x0f\x43ompletePartUrl\x18\x03 \x01(\t\x12\x62\n\x12\x43ompleteUrlHeaders\x18\x04 \x03(\x0b\x32\x46.Volcengine.Vod.Models.Business.PartUploadInfo.CompleteUrlHeadersEntry\x1a\x39\n\x17\x43ompleteUrlHeadersEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t:\x02\x38\x01\"b\n\x19VodCommitUploadInfoResult\x12\x45\n\x04\x44\x61ta\x18\x01 \x01(\x0b\x32\x37.Volcengine.Vod.Models.Business.VodCommitUploadInfoData\"\x89\x01\n\x17VodCommitUploadInfoData\x12\x0b\n\x03Vid\x18\x01 \x01(\t\x12\x11\n\tPosterUri\x18\x02 \x01(\t\x12\x41\n\nSourceInfo\x18\x03 \x01(\x0b\x32-.Volcengine.Vod.Models.Business.VodSourceInfo\x12\x0b\n\x03Mid\x18\x04 \x01(\t\"\xbc\x03\n\x16VodUploadFunctionInput\x12\x14\n\x0cSnapshotTime\x18\x01 \x01(\x01\x12\r\n\x05Title\x18\x02 \x01(\t\x12\x0c\n\x04Tags\x18\x03 \x01(\t\x12\x13\n\x0b\x44\x65scription\x18\x04 \x01(\t\x12\x10\n\x08\x43\x61tegory\x18\x05 \x01(\t\x12\x12\n\nRecordType\x18\x06 \x01(\x05\x12\x0e\n\x06\x46ormat\x18\x07 \x01(\t\x12\x18\n\x10\x43lassificationId\x18\x08 \x01(\x05\x12\x12\n\nTemplateId\x18\t \x01(\t\x12\x0b\n\x03Vid\x18\n \x01(\t\x12\x0b\n\x03\x46id\x18\x0b \x01(\t\x12\x10\n\x08Language\x18\x0c \x01(\t\x12\x10\n\x08StoreUri\x18\r \x01(\t\x12\x0e\n\x06Source\x18\x0e \x01(\t\x12\x0b\n\x03Tag\x18\x0f \x01(\t\x12\x13\n\x0b\x41utoPublish\x18\x10 \x01(\x08\x12\x12\n\nActionType\x18\x11 \x01(\t\x12\x16\n\x0eIsHlsIndexOnly\x18\x12 \x01(\x08\x12\x14\n\x0cHlsMediaSize\x18\x13 \x01(\t\x12\x44\n\tTemplates\x18\x14 \x03(\x0b\x32\x31.Volcengine.Vod.Models.Business.VodUploadTemplate\"h\n\x11VodUploadFunction\x12\x0c\n\x04Name\x18\x01 \x01(\t\x12\x45\n\x05Input\x18\x02 \x01(\x0b\x32\x36.Volcengine.Vod.Models.Business.VodUploadFunctionInput\"\xc8\x01\n\x15\x43ommitUploadInfoParam\x12\x11\n\tSpaceName\x18\x01 \x01(\t\x12\x14\n\x0c\x43\x61llbackArgs\x18\x02 \x01(\t\x12\x12\n\nSessionKey\x18\x03 \x01(\t\x12\x44\n\tFunctions\x18\x04 \x03(\x0b\x32\x31.Volcengine.Vod.Models.Business.VodUploadFunction\x12\x13\n\x0bGetMetaMode\x18\x05 \x01(\t\x12\x17\n\x0fVodUploadSource\x18\x06 \x01(\t\"\x95\x01\n\x15\x43ommitRequestBodyJson\x12\x11\n\tSpaceName\x18\x01 \x01(\t\x12\x12\n\nSessionKey\x18\x02 \x01(\t\x12\x14\n\x0c\x43\x61llbackArgs\x18\x03 \x01(\t\x12\x11\n\tFunctions\x18\x04 \x01(\t\x12\x13\n\x0bGetMetaMode\x18\x05 \x01(\t\x12\x17\n\x0fVodUploadSource\x18\x06 \x01(\t\"\xb8\x02\n\x14\x41pplyUploadInfoParam\x12\x11\n\tSpaceName\x18\x01 \x01(\t\x12\x10\n\x08\x46ileType\x18\x02 \x01(\t\x12\x12\n\nSessionKey\x18\x03 \x01(\t\x12\x10\n\x08\x46ileSize\x18\x04 \x01(\x01\x12\x11\n\tMediaType\x18\x05 \x01(\t\x12\x0f\n\x07TosKeys\x18\x06 \x01(\t\x12\x15\n\rFileExtension\x18\x07 \x01(\t\x12\x12\n\nFilePrefix\x18\x08 \x01(\t\x12\x17\n\x0f\x46lushUploadMode\x18\t \x01(\x05\x12\x0b\n\x03Md5\x18\n \x01(\t\x12\x14\n\x0cStorageClass\x18\x0b \x01(\x05\x12\x18\n\x10UploadHostPrefer\x18\x0c \x01(\t\x12\x19\n\x11\x43lientNetWorkMode\x18\x65 \x01(\t\x12\x15\n\rClientIDCMode\x18\x66 \x01(\t\"\x96\x01\n\x0e\x43ommitResponse\x12\x0b\n\x03Vid\x18\x01 \x01(\t\x12\x0b\n\x03Mid\x18\x02 \x01(\t\x12\x41\n\nSourceInfo\x18\x03 \x01(\x0b\x32-.Volcengine.Vod.Models.Business.VodSourceInfo\x12\x11\n\tPosterUri\x18\x04 \x01(\t\x12\x14\n\x0c\x43\x61llbackArgs\x18\x05 \x01(\t\">\n\x11VodUploadTemplate\x12\x13\n\x0bTemplateIds\x18\x01 \x03(\t\x12\x14\n\x0cTemplateType\x18\x02 \x01(\t\"\x84\x01\n\x13VodUploadOptionInfo\x12\x12\n\nTemplateId\x18\x01 \x01(\t\x12\x13\n\x0bTemplateIds\x18\x02 \x03(\t\x12\x44\n\tTemplates\x18\x03 \x03(\x0b\x32\x31.Volcengine.Vod.Models.Business.VodUploadTemplate\"\x98\x02\n\x15VodUploadCallbackData\x12\x0c\n\x04\x43ode\x18\x01 \x01(\t\x12\x0f\n\x07Message\x18\x02 \x01(\t\x12\x14\n\x0c\x43\x61llbackArgs\x18\x03 \x01(\t\x12\x0b\n\x03Vid\x18\x04 \x01(\t\x12\x0b\n\x03Mid\x18\x05 \x01(\t\x12\x11\n\tSpaceName\x18\x06 \x01(\t\x12\x41\n\nSourceInfo\x18\x07 \x01(\x0b\x32-.Volcengine.Vod.Models.Business.VodSourceInfo\x12\x11\n\tPosterUri\x18\x08 \x01(\t\x12G\n\nOptionInfo\x18\t \x01(\x0b\x32\x33.Volcengine.Vod.Models.Business.VodUploadOptionInfo\"\xa1\x01\n\x10\x43\x61llbackResponse\x12\x11\n\tRequestId\x18\x01 \x01(\t\x12\x0f\n\x07Version\x18\x02 \x01(\t\x12\x11\n\tEventTime\x18\x03 \x01(\t\x12\x11\n\tEventType\x18\x04 \x01(\t\x12\x43\n\x04\x44\x61ta\x18\x05 \x01(\x0b\x32\x35.Volcengine.Vod.Models.Business.VodUploadCallbackData\"+\n\tStoreInfo\x12\x10\n\x08StoreUri\x18\x01 \x01(\t\x12\x0c\n\x04\x41uth\x18\x02 \x01(\t\"(\n\nHeaderPair\x12\x0b\n\x03Key\x18\x01 \x01(\t\x12\r\n\x05Value\x18\x02 \x01(\t\"\xb9\x01\n\rUploadAddress\x12=\n\nStoreInfos\x18\x01 \x03(\x0b\x32).Volcengine.Vod.Models.Business.StoreInfo\x12\x13\n\x0bUploadHosts\x18\x02 \x03(\t\x12@\n\x0cUploadHeader\x18\x03 \x03(\x0b\x32*.Volcengine.Vod.Models.Business.HeaderPair\x12\x12\n\nSessionKey\x18\x04 \x01(\t\"\xae\x01\n\x11\x46lushUploadResult\x12\x13\n\x0b\x46lushUpload\x18\x01 \x01(\x08\x12\x0b\n\x03Vid\x18\x02 \x01(\t\x12\x0b\n\x03Mid\x18\x03 \x01(\t\x12\x41\n\nSourceInfo\x18\x04 \x01(\x0b\x32-.Volcengine.Vod.Models.Business.VodSourceInfo\x12\x11\n\tPosterUri\x18\x05 \x01(\t\x12\x14\n\x0c\x43\x61llbackArgs\x18\x06 \x01(\t\"\x87\x02\n\rApplyResponse\x12\x44\n\rUploadAddress\x18\x01 \x01(\x0b\x32-.Volcengine.Vod.Models.Business.UploadAddress\x12L\n\x11\x46lushUploadResult\x18\x02 \x01(\x0b\x32\x31.Volcengine.Vod.Models.Business.FlushUploadResult\x12\x10\n\x08SDKParam\x18\x03 \x01(\t\x12P\n\x13VpcTosUploadAddress\x18\x05 \x01(\x0b\x32\x33.Volcengine.Vod.Models.Business.VpcTosUploadAddress\"\xa7\x01\n\x19SubmitMoveObjectTaskParam\x12\x13\n\x0bSourceSpace\x18\x01 \x01(\t\x12\x16\n\x0eSourceFileName\x18\x02 \x01(\t\x12\x13\n\x0bTargetSpace\x18\x03 \x01(\t\x12\x16\n\x0eTargetFileName\x18\x04 \x01(\t\x12\x18\n\x10SaveSourceObject\x18\x05 \x01(\x08\x12\x16\n\x0e\x46orceOverwrite\x18\x06 \x01(\x08\"m\n\x1fVodSubmitMoveObjectTaskRespData\x12J\n\x04\x44\x61ta\x18\x01 \x01(\x0b\x32<.Volcengine.Vod.Models.Business.SubmitMoveObjectTaskRespData\"X\n\x1cSubmitMoveObjectTaskRespData\x12\x0e\n\x06TaskId\x18\x01 \x01(\t\x12\x13\n\x0bSourceSpace\x18\x02 \x01(\t\x12\x13\n\x0bTargetSpace\x18\x03 \x01(\t\"X\n\x1cQueryMoveObjectTaskInfoParam\x12\x0e\n\x06TaskId\x18\x01 \x01(\t\x12\x13\n\x0bSourceSpace\x18\x02 \x01(\t\x12\x13\n\x0bTargetSpace\x18\x03 \x01(\t\"r\n!VodQueryMoveObjectTaskInfoResData\x12M\n\x04\x44\x61ta\x18\x01 \x01(\x0b\x32?.Volcengine.Vod.Models.Business.QueryMoveObjectTaskInfoRespData\"\x81\x01\n\x1fQueryMoveObjectTaskInfoRespData\x12\x0e\n\x06TaskId\x18\x01 \x01(\t\x12\x13\n\x0bSourceSpace\x18\x02 \x01(\t\x12\x13\n\x0bTargetSpace\x18\x03 \x01(\t\x12\r\n\x05State\x18\x04 \x01(\t\x12\x15\n\rTaskRunResult\x18\x05 \x01(\t*B\n\x10StorageClassType\x12\x0b\n\x07\x44\x65\x66\x61ult\x10\x00\x12\x0c\n\x08Standard\x10\x01\x12\x0b\n\x07\x41rchive\x10\x02\x12\x06\n\x02IA\x10\x03\x42\xcd\x01\n)com.volcengine.service.vod.model.businessB\tVodUploadP\x01ZAgithub.com/volcengine/volc-sdk-golang/service/vod/models/business\xa0\x01\x01\xd8\x01\x01\xc2\x02\x00\xca\x02 Volc\\Service\\Vod\\Models\\Business\xe2\x02#Volc\\Service\\Vod\\Models\\GPBMetadatab\x06proto3')

_STORAGECLASSTYPE = DESCRIPTOR.enum_types_by_name['StorageClassType']
StorageClassType = enum_type_wrapper.EnumTypeWrapper(_STORAGECLASSTYPE)
Default = 0
Standard = 1
Archive = 2
IA = 3


_VODURLUPLOADURLSET = DESCRIPTOR.message_types_by_name['VodUrlUploadURLSet']
_VODURLUPLOADURLSET_CUSTOMURLHEADERSENTRY = _VODURLUPLOADURLSET.nested_types_by_name['CustomURLHeadersEntry']
_VODIMAGEFILE = DESCRIPTOR.message_types_by_name['VodImageFile']
_VODEXECUTION = DESCRIPTOR.message_types_by_name['VodExecution']
_VODEXECUTIONOPERATION = DESCRIPTOR.message_types_by_name['VodExecutionOperation']
_VODEXECUTIONOPERATIONTASK = DESCRIPTOR.message_types_by_name['VodExecutionOperationTask']
_VODEXECUTIONOPERATIONTASKOCR = DESCRIPTOR.message_types_by_name['VodExecutionOperationTaskOcr']
_VODEXECUTIONOPERATIONTASKASR = DESCRIPTOR.message_types_by_name['VodExecutionOperationTaskAsr']
_VODEXECUTIONCONTROL = DESCRIPTOR.message_types_by_name['VodExecutionControl']
_VODURLRESPONSEDATA = DESCRIPTOR.message_types_by_name['VodUrlResponseData']
_VALUEPAIR = DESCRIPTOR.message_types_by_name['ValuePair']
_VODQUERYDATA = DESCRIPTOR.message_types_by_name['VodQueryData']
_VODQUERYUPLOADRESULT = DESCRIPTOR.message_types_by_name['VodQueryUploadResult']
_VODCOMMITDATA = DESCRIPTOR.message_types_by_name['VodCommitData']
_VODCOMMITUPLOADINFORESPONSEDATA = DESCRIPTOR.message_types_by_name['VodCommitUploadInfoResponseData']
_VODURLSET = DESCRIPTOR.message_types_by_name['VodURLSet']
_VODAPPLYUPLOADINFORESULT = DESCRIPTOR.message_types_by_name['VodApplyUploadInfoResult']
_VODAPPLYUPLOADINFODATA = DESCRIPTOR.message_types_by_name['VodApplyUploadInfoData']
_VODUPLOADADDRESS = DESCRIPTOR.message_types_by_name['VodUploadAddress']
_CANDIDATEUPLOADADDRESSES = DESCRIPTOR.message_types_by_name['CandidateUploadAddresses']
_VODSTOREINFO = DESCRIPTOR.message_types_by_name['VodStoreInfo']
_VODHEADERPAIR = DESCRIPTOR.message_types_by_name['VodHeaderPair']
_VPCTOSUPLOADADDRESS = DESCRIPTOR.message_types_by_name['VpcTosUploadAddress']
_VPCTOSUPLOADADDRESS_PUTURLHEADERSENTRY = _VPCTOSUPLOADADDRESS.nested_types_by_name['PutUrlHeadersEntry']
_PARTUPLOADINFO = DESCRIPTOR.message_types_by_name['PartUploadInfo']
_PARTUPLOADINFO_COMPLETEURLHEADERSENTRY = _PARTUPLOADINFO.nested_types_by_name['CompleteUrlHeadersEntry']
_VODCOMMITUPLOADINFORESULT = DESCRIPTOR.message_types_by_name['VodCommitUploadInfoResult']
_VODCOMMITUPLOADINFODATA = DESCRIPTOR.message_types_by_name['VodCommitUploadInfoData']
_VODUPLOADFUNCTIONINPUT = DESCRIPTOR.message_types_by_name['VodUploadFunctionInput']
_VODUPLOADFUNCTION = DESCRIPTOR.message_types_by_name['VodUploadFunction']
_COMMITUPLOADINFOPARAM = DESCRIPTOR.message_types_by_name['CommitUploadInfoParam']
_COMMITREQUESTBODYJSON = DESCRIPTOR.message_types_by_name['CommitRequestBodyJson']
_APPLYUPLOADINFOPARAM = DESCRIPTOR.message_types_by_name['ApplyUploadInfoParam']
_COMMITRESPONSE = DESCRIPTOR.message_types_by_name['CommitResponse']
_VODUPLOADTEMPLATE = DESCRIPTOR.message_types_by_name['VodUploadTemplate']
_VODUPLOADOPTIONINFO = DESCRIPTOR.message_types_by_name['VodUploadOptionInfo']
_VODUPLOADCALLBACKDATA = DESCRIPTOR.message_types_by_name['VodUploadCallbackData']
_CALLBACKRESPONSE = DESCRIPTOR.message_types_by_name['CallbackResponse']
_STOREINFO = DESCRIPTOR.message_types_by_name['StoreInfo']
_HEADERPAIR = DESCRIPTOR.message_types_by_name['HeaderPair']
_UPLOADADDRESS = DESCRIPTOR.message_types_by_name['UploadAddress']
_FLUSHUPLOADRESULT = DESCRIPTOR.message_types_by_name['FlushUploadResult']
_APPLYRESPONSE = DESCRIPTOR.message_types_by_name['ApplyResponse']
_SUBMITMOVEOBJECTTASKPARAM = DESCRIPTOR.message_types_by_name['SubmitMoveObjectTaskParam']
_VODSUBMITMOVEOBJECTTASKRESPDATA = DESCRIPTOR.message_types_by_name['VodSubmitMoveObjectTaskRespData']
_SUBMITMOVEOBJECTTASKRESPDATA = DESCRIPTOR.message_types_by_name['SubmitMoveObjectTaskRespData']
_QUERYMOVEOBJECTTASKINFOPARAM = DESCRIPTOR.message_types_by_name['QueryMoveObjectTaskInfoParam']
_VODQUERYMOVEOBJECTTASKINFORESDATA = DESCRIPTOR.message_types_by_name['VodQueryMoveObjectTaskInfoResData']
_QUERYMOVEOBJECTTASKINFORESPDATA = DESCRIPTOR.message_types_by_name['QueryMoveObjectTaskInfoRespData']
VodUrlUploadURLSet = _reflection.GeneratedProtocolMessageType('VodUrlUploadURLSet', (_message.Message,), {

  'CustomURLHeadersEntry' : _reflection.GeneratedProtocolMessageType('CustomURLHeadersEntry', (_message.Message,), {
    'DESCRIPTOR' : _VODURLUPLOADURLSET_CUSTOMURLHEADERSENTRY,
    '__module__' : 'volcengine.vod.business.vod_upload_pb2'
    # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.VodUrlUploadURLSet.CustomURLHeadersEntry)
    })
  ,
  'DESCRIPTOR' : _VODURLUPLOADURLSET,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.VodUrlUploadURLSet)
  })
_sym_db.RegisterMessage(VodUrlUploadURLSet)
_sym_db.RegisterMessage(VodUrlUploadURLSet.CustomURLHeadersEntry)

VodImageFile = _reflection.GeneratedProtocolMessageType('VodImageFile', (_message.Message,), {
  'DESCRIPTOR' : _VODIMAGEFILE,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.VodImageFile)
  })
_sym_db.RegisterMessage(VodImageFile)

VodExecution = _reflection.GeneratedProtocolMessageType('VodExecution', (_message.Message,), {
  'DESCRIPTOR' : _VODEXECUTION,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.VodExecution)
  })
_sym_db.RegisterMessage(VodExecution)

VodExecutionOperation = _reflection.GeneratedProtocolMessageType('VodExecutionOperation', (_message.Message,), {
  'DESCRIPTOR' : _VODEXECUTIONOPERATION,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.VodExecutionOperation)
  })
_sym_db.RegisterMessage(VodExecutionOperation)

VodExecutionOperationTask = _reflection.GeneratedProtocolMessageType('VodExecutionOperationTask', (_message.Message,), {
  'DESCRIPTOR' : _VODEXECUTIONOPERATIONTASK,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.VodExecutionOperationTask)
  })
_sym_db.RegisterMessage(VodExecutionOperationTask)

VodExecutionOperationTaskOcr = _reflection.GeneratedProtocolMessageType('VodExecutionOperationTaskOcr', (_message.Message,), {
  'DESCRIPTOR' : _VODEXECUTIONOPERATIONTASKOCR,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.VodExecutionOperationTaskOcr)
  })
_sym_db.RegisterMessage(VodExecutionOperationTaskOcr)

VodExecutionOperationTaskAsr = _reflection.GeneratedProtocolMessageType('VodExecutionOperationTaskAsr', (_message.Message,), {
  'DESCRIPTOR' : _VODEXECUTIONOPERATIONTASKASR,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.VodExecutionOperationTaskAsr)
  })
_sym_db.RegisterMessage(VodExecutionOperationTaskAsr)

VodExecutionControl = _reflection.GeneratedProtocolMessageType('VodExecutionControl', (_message.Message,), {
  'DESCRIPTOR' : _VODEXECUTIONCONTROL,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.VodExecutionControl)
  })
_sym_db.RegisterMessage(VodExecutionControl)

VodUrlResponseData = _reflection.GeneratedProtocolMessageType('VodUrlResponseData', (_message.Message,), {
  'DESCRIPTOR' : _VODURLRESPONSEDATA,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.VodUrlResponseData)
  })
_sym_db.RegisterMessage(VodUrlResponseData)

ValuePair = _reflection.GeneratedProtocolMessageType('ValuePair', (_message.Message,), {
  'DESCRIPTOR' : _VALUEPAIR,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.ValuePair)
  })
_sym_db.RegisterMessage(ValuePair)

VodQueryData = _reflection.GeneratedProtocolMessageType('VodQueryData', (_message.Message,), {
  'DESCRIPTOR' : _VODQUERYDATA,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.VodQueryData)
  })
_sym_db.RegisterMessage(VodQueryData)

VodQueryUploadResult = _reflection.GeneratedProtocolMessageType('VodQueryUploadResult', (_message.Message,), {
  'DESCRIPTOR' : _VODQUERYUPLOADRESULT,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.VodQueryUploadResult)
  })
_sym_db.RegisterMessage(VodQueryUploadResult)

VodCommitData = _reflection.GeneratedProtocolMessageType('VodCommitData', (_message.Message,), {
  'DESCRIPTOR' : _VODCOMMITDATA,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.VodCommitData)
  })
_sym_db.RegisterMessage(VodCommitData)

VodCommitUploadInfoResponseData = _reflection.GeneratedProtocolMessageType('VodCommitUploadInfoResponseData', (_message.Message,), {
  'DESCRIPTOR' : _VODCOMMITUPLOADINFORESPONSEDATA,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.VodCommitUploadInfoResponseData)
  })
_sym_db.RegisterMessage(VodCommitUploadInfoResponseData)

VodURLSet = _reflection.GeneratedProtocolMessageType('VodURLSet', (_message.Message,), {
  'DESCRIPTOR' : _VODURLSET,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.VodURLSet)
  })
_sym_db.RegisterMessage(VodURLSet)

VodApplyUploadInfoResult = _reflection.GeneratedProtocolMessageType('VodApplyUploadInfoResult', (_message.Message,), {
  'DESCRIPTOR' : _VODAPPLYUPLOADINFORESULT,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.VodApplyUploadInfoResult)
  })
_sym_db.RegisterMessage(VodApplyUploadInfoResult)

VodApplyUploadInfoData = _reflection.GeneratedProtocolMessageType('VodApplyUploadInfoData', (_message.Message,), {
  'DESCRIPTOR' : _VODAPPLYUPLOADINFODATA,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.VodApplyUploadInfoData)
  })
_sym_db.RegisterMessage(VodApplyUploadInfoData)

VodUploadAddress = _reflection.GeneratedProtocolMessageType('VodUploadAddress', (_message.Message,), {
  'DESCRIPTOR' : _VODUPLOADADDRESS,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.VodUploadAddress)
  })
_sym_db.RegisterMessage(VodUploadAddress)

CandidateUploadAddresses = _reflection.GeneratedProtocolMessageType('CandidateUploadAddresses', (_message.Message,), {
  'DESCRIPTOR' : _CANDIDATEUPLOADADDRESSES,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.CandidateUploadAddresses)
  })
_sym_db.RegisterMessage(CandidateUploadAddresses)

VodStoreInfo = _reflection.GeneratedProtocolMessageType('VodStoreInfo', (_message.Message,), {
  'DESCRIPTOR' : _VODSTOREINFO,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.VodStoreInfo)
  })
_sym_db.RegisterMessage(VodStoreInfo)

VodHeaderPair = _reflection.GeneratedProtocolMessageType('VodHeaderPair', (_message.Message,), {
  'DESCRIPTOR' : _VODHEADERPAIR,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.VodHeaderPair)
  })
_sym_db.RegisterMessage(VodHeaderPair)

VpcTosUploadAddress = _reflection.GeneratedProtocolMessageType('VpcTosUploadAddress', (_message.Message,), {

  'PutUrlHeadersEntry' : _reflection.GeneratedProtocolMessageType('PutUrlHeadersEntry', (_message.Message,), {
    'DESCRIPTOR' : _VPCTOSUPLOADADDRESS_PUTURLHEADERSENTRY,
    '__module__' : 'volcengine.vod.business.vod_upload_pb2'
    # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.VpcTosUploadAddress.PutUrlHeadersEntry)
    })
  ,
  'DESCRIPTOR' : _VPCTOSUPLOADADDRESS,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.VpcTosUploadAddress)
  })
_sym_db.RegisterMessage(VpcTosUploadAddress)
_sym_db.RegisterMessage(VpcTosUploadAddress.PutUrlHeadersEntry)

PartUploadInfo = _reflection.GeneratedProtocolMessageType('PartUploadInfo', (_message.Message,), {

  'CompleteUrlHeadersEntry' : _reflection.GeneratedProtocolMessageType('CompleteUrlHeadersEntry', (_message.Message,), {
    'DESCRIPTOR' : _PARTUPLOADINFO_COMPLETEURLHEADERSENTRY,
    '__module__' : 'volcengine.vod.business.vod_upload_pb2'
    # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.PartUploadInfo.CompleteUrlHeadersEntry)
    })
  ,
  'DESCRIPTOR' : _PARTUPLOADINFO,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.PartUploadInfo)
  })
_sym_db.RegisterMessage(PartUploadInfo)
_sym_db.RegisterMessage(PartUploadInfo.CompleteUrlHeadersEntry)

VodCommitUploadInfoResult = _reflection.GeneratedProtocolMessageType('VodCommitUploadInfoResult', (_message.Message,), {
  'DESCRIPTOR' : _VODCOMMITUPLOADINFORESULT,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.VodCommitUploadInfoResult)
  })
_sym_db.RegisterMessage(VodCommitUploadInfoResult)

VodCommitUploadInfoData = _reflection.GeneratedProtocolMessageType('VodCommitUploadInfoData', (_message.Message,), {
  'DESCRIPTOR' : _VODCOMMITUPLOADINFODATA,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.VodCommitUploadInfoData)
  })
_sym_db.RegisterMessage(VodCommitUploadInfoData)

VodUploadFunctionInput = _reflection.GeneratedProtocolMessageType('VodUploadFunctionInput', (_message.Message,), {
  'DESCRIPTOR' : _VODUPLOADFUNCTIONINPUT,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.VodUploadFunctionInput)
  })
_sym_db.RegisterMessage(VodUploadFunctionInput)

VodUploadFunction = _reflection.GeneratedProtocolMessageType('VodUploadFunction', (_message.Message,), {
  'DESCRIPTOR' : _VODUPLOADFUNCTION,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.VodUploadFunction)
  })
_sym_db.RegisterMessage(VodUploadFunction)

CommitUploadInfoParam = _reflection.GeneratedProtocolMessageType('CommitUploadInfoParam', (_message.Message,), {
  'DESCRIPTOR' : _COMMITUPLOADINFOPARAM,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.CommitUploadInfoParam)
  })
_sym_db.RegisterMessage(CommitUploadInfoParam)

CommitRequestBodyJson = _reflection.GeneratedProtocolMessageType('CommitRequestBodyJson', (_message.Message,), {
  'DESCRIPTOR' : _COMMITREQUESTBODYJSON,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.CommitRequestBodyJson)
  })
_sym_db.RegisterMessage(CommitRequestBodyJson)

ApplyUploadInfoParam = _reflection.GeneratedProtocolMessageType('ApplyUploadInfoParam', (_message.Message,), {
  'DESCRIPTOR' : _APPLYUPLOADINFOPARAM,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.ApplyUploadInfoParam)
  })
_sym_db.RegisterMessage(ApplyUploadInfoParam)

CommitResponse = _reflection.GeneratedProtocolMessageType('CommitResponse', (_message.Message,), {
  'DESCRIPTOR' : _COMMITRESPONSE,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.CommitResponse)
  })
_sym_db.RegisterMessage(CommitResponse)

VodUploadTemplate = _reflection.GeneratedProtocolMessageType('VodUploadTemplate', (_message.Message,), {
  'DESCRIPTOR' : _VODUPLOADTEMPLATE,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.VodUploadTemplate)
  })
_sym_db.RegisterMessage(VodUploadTemplate)

VodUploadOptionInfo = _reflection.GeneratedProtocolMessageType('VodUploadOptionInfo', (_message.Message,), {
  'DESCRIPTOR' : _VODUPLOADOPTIONINFO,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.VodUploadOptionInfo)
  })
_sym_db.RegisterMessage(VodUploadOptionInfo)

VodUploadCallbackData = _reflection.GeneratedProtocolMessageType('VodUploadCallbackData', (_message.Message,), {
  'DESCRIPTOR' : _VODUPLOADCALLBACKDATA,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.VodUploadCallbackData)
  })
_sym_db.RegisterMessage(VodUploadCallbackData)

CallbackResponse = _reflection.GeneratedProtocolMessageType('CallbackResponse', (_message.Message,), {
  'DESCRIPTOR' : _CALLBACKRESPONSE,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.CallbackResponse)
  })
_sym_db.RegisterMessage(CallbackResponse)

StoreInfo = _reflection.GeneratedProtocolMessageType('StoreInfo', (_message.Message,), {
  'DESCRIPTOR' : _STOREINFO,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.StoreInfo)
  })
_sym_db.RegisterMessage(StoreInfo)

HeaderPair = _reflection.GeneratedProtocolMessageType('HeaderPair', (_message.Message,), {
  'DESCRIPTOR' : _HEADERPAIR,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.HeaderPair)
  })
_sym_db.RegisterMessage(HeaderPair)

UploadAddress = _reflection.GeneratedProtocolMessageType('UploadAddress', (_message.Message,), {
  'DESCRIPTOR' : _UPLOADADDRESS,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.UploadAddress)
  })
_sym_db.RegisterMessage(UploadAddress)

FlushUploadResult = _reflection.GeneratedProtocolMessageType('FlushUploadResult', (_message.Message,), {
  'DESCRIPTOR' : _FLUSHUPLOADRESULT,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.FlushUploadResult)
  })
_sym_db.RegisterMessage(FlushUploadResult)

ApplyResponse = _reflection.GeneratedProtocolMessageType('ApplyResponse', (_message.Message,), {
  'DESCRIPTOR' : _APPLYRESPONSE,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.ApplyResponse)
  })
_sym_db.RegisterMessage(ApplyResponse)

SubmitMoveObjectTaskParam = _reflection.GeneratedProtocolMessageType('SubmitMoveObjectTaskParam', (_message.Message,), {
  'DESCRIPTOR' : _SUBMITMOVEOBJECTTASKPARAM,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.SubmitMoveObjectTaskParam)
  })
_sym_db.RegisterMessage(SubmitMoveObjectTaskParam)

VodSubmitMoveObjectTaskRespData = _reflection.GeneratedProtocolMessageType('VodSubmitMoveObjectTaskRespData', (_message.Message,), {
  'DESCRIPTOR' : _VODSUBMITMOVEOBJECTTASKRESPDATA,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.VodSubmitMoveObjectTaskRespData)
  })
_sym_db.RegisterMessage(VodSubmitMoveObjectTaskRespData)

SubmitMoveObjectTaskRespData = _reflection.GeneratedProtocolMessageType('SubmitMoveObjectTaskRespData', (_message.Message,), {
  'DESCRIPTOR' : _SUBMITMOVEOBJECTTASKRESPDATA,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.SubmitMoveObjectTaskRespData)
  })
_sym_db.RegisterMessage(SubmitMoveObjectTaskRespData)

QueryMoveObjectTaskInfoParam = _reflection.GeneratedProtocolMessageType('QueryMoveObjectTaskInfoParam', (_message.Message,), {
  'DESCRIPTOR' : _QUERYMOVEOBJECTTASKINFOPARAM,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.QueryMoveObjectTaskInfoParam)
  })
_sym_db.RegisterMessage(QueryMoveObjectTaskInfoParam)

VodQueryMoveObjectTaskInfoResData = _reflection.GeneratedProtocolMessageType('VodQueryMoveObjectTaskInfoResData', (_message.Message,), {
  'DESCRIPTOR' : _VODQUERYMOVEOBJECTTASKINFORESDATA,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.VodQueryMoveObjectTaskInfoResData)
  })
_sym_db.RegisterMessage(VodQueryMoveObjectTaskInfoResData)

QueryMoveObjectTaskInfoRespData = _reflection.GeneratedProtocolMessageType('QueryMoveObjectTaskInfoRespData', (_message.Message,), {
  'DESCRIPTOR' : _QUERYMOVEOBJECTTASKINFORESPDATA,
  '__module__' : 'volcengine.vod.business.vod_upload_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Vod.Models.Business.QueryMoveObjectTaskInfoRespData)
  })
_sym_db.RegisterMessage(QueryMoveObjectTaskInfoRespData)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n)com.volcengine.service.vod.model.businessB\tVodUploadP\001ZAgithub.com/volcengine/volc-sdk-golang/service/vod/models/business\240\001\001\330\001\001\302\002\000\312\002 Volc\\Service\\Vod\\Models\\Business\342\002#Volc\\Service\\Vod\\Models\\GPBMetadata'
  _VODURLUPLOADURLSET_CUSTOMURLHEADERSENTRY._options = None
  _VODURLUPLOADURLSET_CUSTOMURLHEADERSENTRY._serialized_options = b'8\001'
  _VPCTOSUPLOADADDRESS_PUTURLHEADERSENTRY._options = None
  _VPCTOSUPLOADADDRESS_PUTURLHEADERSENTRY._serialized_options = b'8\001'
  _PARTUPLOADINFO_COMPLETEURLHEADERSENTRY._options = None
  _PARTUPLOADINFO_COMPLETEURLHEADERSENTRY._serialized_options = b'8\001'
  _STORAGECLASSTYPE._serialized_start=7576
  _STORAGECLASSTYPE._serialized_end=7642
  _VODURLUPLOADURLSET._serialized_start=119
  _VODURLUPLOADURLSET._serialized_end=804
  _VODURLUPLOADURLSET_CUSTOMURLHEADERSENTRY._serialized_start=749
  _VODURLUPLOADURLSET_CUSTOMURLHEADERSENTRY._serialized_end=804
  _VODIMAGEFILE._serialized_start=806
  _VODIMAGEFILE._serialized_end=851
  _VODEXECUTION._serialized_start=854
  _VODEXECUTION._serialized_end=1012
  _VODEXECUTIONOPERATION._serialized_start=1014
  _VODEXECUTIONOPERATION._serialized_end=1124
  _VODEXECUTIONOPERATIONTASK._serialized_start=1127
  _VODEXECUTIONOPERATIONTASK._serialized_end=1318
  _VODEXECUTIONOPERATIONTASKOCR._serialized_start=1320
  _VODEXECUTIONOPERATIONTASKOCR._serialized_end=1386
  _VODEXECUTIONOPERATIONTASKASR._serialized_start=1388
  _VODEXECUTIONOPERATIONTASKASR._serialized_end=1513
  _VODEXECUTIONCONTROL._serialized_start=1515
  _VODEXECUTIONCONTROL._serialized_end=1579
  _VODURLRESPONSEDATA._serialized_start=1581
  _VODURLRESPONSEDATA._serialized_end=1658
  _VALUEPAIR._serialized_start=1660
  _VALUEPAIR._serialized_end=1724
  _VODQUERYDATA._serialized_start=1726
  _VODQUERYDATA._serialized_end=1808
  _VODQUERYUPLOADRESULT._serialized_start=1810
  _VODQUERYUPLOADRESULT._serialized_end=1922
  _VODCOMMITDATA._serialized_start=1924
  _VODCOMMITDATA._serialized_end=2018
  _VODCOMMITUPLOADINFORESPONSEDATA._serialized_start=2021
  _VODCOMMITUPLOADINFORESPONSEDATA._serialized_end=2188
  _VODURLSET._serialized_start=2191
  _VODURLSET._serialized_end=2410
  _VODAPPLYUPLOADINFORESULT._serialized_start=2412
  _VODAPPLYUPLOADINFORESULT._serialized_end=2508
  _VODAPPLYUPLOADINFODATA._serialized_start=2511
  _VODAPPLYUPLOADINFODATA._serialized_end=2782
  _VODUPLOADADDRESS._serialized_start=2785
  _VODUPLOADADDRESS._serialized_end=2979
  _CANDIDATEUPLOADADDRESSES._serialized_start=2982
  _CANDIDATEUPLOADADDRESSES._serialized_end=3242
  _VODSTOREINFO._serialized_start=3244
  _VODSTOREINFO._serialized_end=3290
  _VODHEADERPAIR._serialized_start=3292
  _VODHEADERPAIR._serialized_end=3335
  _VPCTOSUPLOADADDRESS._serialized_start=3338
  _VPCTOSUPLOADADDRESS._serialized_end=3643
  _VPCTOSUPLOADADDRESS_PUTURLHEADERSENTRY._serialized_start=3591
  _VPCTOSUPLOADADDRESS_PUTURLHEADERSENTRY._serialized_end=3643
  _PARTUPLOADINFO._serialized_start=3646
  _PARTUPLOADINFO._serialized_end=3885
  _PARTUPLOADINFO_COMPLETEURLHEADERSENTRY._serialized_start=3828
  _PARTUPLOADINFO_COMPLETEURLHEADERSENTRY._serialized_end=3885
  _VODCOMMITUPLOADINFORESULT._serialized_start=3887
  _VODCOMMITUPLOADINFORESULT._serialized_end=3985
  _VODCOMMITUPLOADINFODATA._serialized_start=3988
  _VODCOMMITUPLOADINFODATA._serialized_end=4125
  _VODUPLOADFUNCTIONINPUT._serialized_start=4128
  _VODUPLOADFUNCTIONINPUT._serialized_end=4572
  _VODUPLOADFUNCTION._serialized_start=4574
  _VODUPLOADFUNCTION._serialized_end=4678
  _COMMITUPLOADINFOPARAM._serialized_start=4681
  _COMMITUPLOADINFOPARAM._serialized_end=4881
  _COMMITREQUESTBODYJSON._serialized_start=4884
  _COMMITREQUESTBODYJSON._serialized_end=5033
  _APPLYUPLOADINFOPARAM._serialized_start=5036
  _APPLYUPLOADINFOPARAM._serialized_end=5348
  _COMMITRESPONSE._serialized_start=5351
  _COMMITRESPONSE._serialized_end=5501
  _VODUPLOADTEMPLATE._serialized_start=5503
  _VODUPLOADTEMPLATE._serialized_end=5565
  _VODUPLOADOPTIONINFO._serialized_start=5568
  _VODUPLOADOPTIONINFO._serialized_end=5700
  _VODUPLOADCALLBACKDATA._serialized_start=5703
  _VODUPLOADCALLBACKDATA._serialized_end=5983
  _CALLBACKRESPONSE._serialized_start=5986
  _CALLBACKRESPONSE._serialized_end=6147
  _STOREINFO._serialized_start=6149
  _STOREINFO._serialized_end=6192
  _HEADERPAIR._serialized_start=6194
  _HEADERPAIR._serialized_end=6234
  _UPLOADADDRESS._serialized_start=6237
  _UPLOADADDRESS._serialized_end=6422
  _FLUSHUPLOADRESULT._serialized_start=6425
  _FLUSHUPLOADRESULT._serialized_end=6599
  _APPLYRESPONSE._serialized_start=6602
  _APPLYRESPONSE._serialized_end=6865
  _SUBMITMOVEOBJECTTASKPARAM._serialized_start=6868
  _SUBMITMOVEOBJECTTASKPARAM._serialized_end=7035
  _VODSUBMITMOVEOBJECTTASKRESPDATA._serialized_start=7037
  _VODSUBMITMOVEOBJECTTASKRESPDATA._serialized_end=7146
  _SUBMITMOVEOBJECTTASKRESPDATA._serialized_start=7148
  _SUBMITMOVEOBJECTTASKRESPDATA._serialized_end=7236
  _QUERYMOVEOBJECTTASKINFOPARAM._serialized_start=7238
  _QUERYMOVEOBJECTTASKINFOPARAM._serialized_end=7326
  _VODQUERYMOVEOBJECTTASKINFORESDATA._serialized_start=7328
  _VODQUERYMOVEOBJECTTASKINFORESDATA._serialized_end=7442
  _QUERYMOVEOBJECTTASKINFORESPDATA._serialized_start=7445
  _QUERYMOVEOBJECTTASKINFORESPDATA._serialized_end=7574
# @@protoc_insertion_point(module_scope)
