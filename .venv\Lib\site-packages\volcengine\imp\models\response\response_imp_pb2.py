# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: imp/response/response_imp.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from volcengine.base.models.base import base_pb2 as volcengine_dot_base_dot_base__pb2
from volcengine.imp.models.business import imp_common_pb2 as imp_dot_business_dot_imp__common__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1fimp/response/response_imp.proto\x12\x1eVolcengine.Imp.Models.Response\x1a\x1avolcengine/base/base.proto\x1a\x1dimp/business/imp_common.proto\"o\n\x14ImpSubmitJobResponse\x12G\n\x10ResponseMetadata\x18\x01 \x01(\x0b\x32-.Volcengine.Base.Models.Base.ResponseMetadata\x12\x0e\n\x06Result\x18\x02 \x01(\t\"]\n\x12ImpKillJobResponse\x12G\n\x10ResponseMetadata\x18\x01 \x01(\x0b\x32-.Volcengine.Base.Models.Base.ResponseMetadata\"\x92\x02\n\x16ImpRetrieveJobResponse\x12G\n\x10ResponseMetadata\x18\x01 \x01(\x0b\x32-.Volcengine.Base.Models.Base.ResponseMetadata\x12R\n\x06Result\x18\x02 \x03(\x0b\x32\x42.Volcengine.Imp.Models.Response.ImpRetrieveJobResponse.ResultEntry\x1a[\n\x0bResultEntry\x12\x0b\n\x03key\x18\x01 \x01(\t\x12;\n\x05value\x18\x02 \x01(\x0b\x32,.Volcengine.Imp.Models.Business.JobExecution:\x02\x38\x01\x42\xcc\x01\n)com.volcengine.service.imp.model.responseB\x0bImpResponseP\x01ZAgithub.com/volcengine/volc-sdk-golang/service/imp/models/response\xa0\x01\x01\xd8\x01\x01\xca\x02 Volc\\Service\\Imp\\Models\\Response\xe2\x02#Volc\\Service\\Imp\\Models\\GPBMetadatab\x06proto3')



_IMPSUBMITJOBRESPONSE = DESCRIPTOR.message_types_by_name['ImpSubmitJobResponse']
_IMPKILLJOBRESPONSE = DESCRIPTOR.message_types_by_name['ImpKillJobResponse']
_IMPRETRIEVEJOBRESPONSE = DESCRIPTOR.message_types_by_name['ImpRetrieveJobResponse']
_IMPRETRIEVEJOBRESPONSE_RESULTENTRY = _IMPRETRIEVEJOBRESPONSE.nested_types_by_name['ResultEntry']
ImpSubmitJobResponse = _reflection.GeneratedProtocolMessageType('ImpSubmitJobResponse', (_message.Message,), {
  'DESCRIPTOR' : _IMPSUBMITJOBRESPONSE,
  '__module__' : 'imp.response.response_imp_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Imp.Models.Response.ImpSubmitJobResponse)
  })
_sym_db.RegisterMessage(ImpSubmitJobResponse)

ImpKillJobResponse = _reflection.GeneratedProtocolMessageType('ImpKillJobResponse', (_message.Message,), {
  'DESCRIPTOR' : _IMPKILLJOBRESPONSE,
  '__module__' : 'imp.response.response_imp_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Imp.Models.Response.ImpKillJobResponse)
  })
_sym_db.RegisterMessage(ImpKillJobResponse)

ImpRetrieveJobResponse = _reflection.GeneratedProtocolMessageType('ImpRetrieveJobResponse', (_message.Message,), {

  'ResultEntry' : _reflection.GeneratedProtocolMessageType('ResultEntry', (_message.Message,), {
    'DESCRIPTOR' : _IMPRETRIEVEJOBRESPONSE_RESULTENTRY,
    '__module__' : 'imp.response.response_imp_pb2'
    # @@protoc_insertion_point(class_scope:Volcengine.Imp.Models.Response.ImpRetrieveJobResponse.ResultEntry)
    })
  ,
  'DESCRIPTOR' : _IMPRETRIEVEJOBRESPONSE,
  '__module__' : 'imp.response.response_imp_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Imp.Models.Response.ImpRetrieveJobResponse)
  })
_sym_db.RegisterMessage(ImpRetrieveJobResponse)
_sym_db.RegisterMessage(ImpRetrieveJobResponse.ResultEntry)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n)com.volcengine.service.imp.model.responseB\013ImpResponseP\001ZAgithub.com/volcengine/volc-sdk-golang/service/imp/models/response\240\001\001\330\001\001\312\002 Volc\\Service\\Imp\\Models\\Response\342\002#Volc\\Service\\Imp\\Models\\GPBMetadata'
  _IMPRETRIEVEJOBRESPONSE_RESULTENTRY._options = None
  _IMPRETRIEVEJOBRESPONSE_RESULTENTRY._serialized_options = b'8\001'
  _IMPSUBMITJOBRESPONSE._serialized_start=126
  _IMPSUBMITJOBRESPONSE._serialized_end=237
  _IMPKILLJOBRESPONSE._serialized_start=239
  _IMPKILLJOBRESPONSE._serialized_end=332
  _IMPRETRIEVEJOBRESPONSE._serialized_start=335
  _IMPRETRIEVEJOBRESPONSE._serialized_end=609
  _IMPRETRIEVEJOBRESPONSE_RESULTENTRY._serialized_start=518
  _IMPRETRIEVEJOBRESPONSE_RESULTENTRY._serialized_end=609
# @@protoc_insertion_point(module_scope)
