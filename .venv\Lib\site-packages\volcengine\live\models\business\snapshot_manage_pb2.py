# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: live/business/snapshot_manage.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n#live/business/snapshot_manage.proto\x12\x1fVolcengine.Live.Models.Business\"\xb2\x01\n\x12\x43\x44NSnapshotHistory\x12\r\n\x05Vhost\x18\x01 \x01(\t\x12\x0b\n\x03\x41pp\x18\x02 \x01(\t\x12\x0e\n\x06Stream\x18\x03 \x01(\t\x12\x0c\n\x04Path\x18\x04 \x01(\t\x12\x10\n\x08\x46ileName\x18\x05 \x01(\t\x12\x11\n\tTimeStamp\x18\x06 \x01(\t\x12\r\n\x05Width\x18\x07 \x01(\x03\x12\x0e\n\x06Height\x18\x08 \x01(\x03\x12\x11\n\tServiceID\x18\t \x01(\t\x12\x0b\n\x03URI\x18\n \x01(\t\"V\n\nPagination\x12\x0f\n\x07PageCur\x18\x01 \x01(\x03\x12\x10\n\x08PageSize\x18\x02 \x01(\x03\x12\x11\n\tPageTotal\x18\x03 \x01(\x03\x12\x12\n\nTotalCount\x18\x04 \x01(\x03\"\x9c\x01\n\x16\x43\x44NSnapshotHistoryInfo\x12\x41\n\x04\x44\x61ta\x18\x01 \x03(\x0b\x32\x33.Volcengine.Live.Models.Business.CDNSnapshotHistory\x12?\n\nPagination\x18\x02 \x01(\x0b\x32+.Volcengine.Live.Models.Business.PaginationB\xd6\x01\n*com.volcengine.service.live.model.businessB\x0eSnapshotManageP\x01ZBgithub.com/volcengine/volc-sdk-golang/service/live/models/business\xa0\x01\x01\xd8\x01\x01\xc2\x02\x00\xca\x02!Volc\\Service\\Live\\Models\\Business\xe2\x02$Volc\\Service\\Live\\Models\\GPBMetadatab\x06proto3')



_CDNSNAPSHOTHISTORY = DESCRIPTOR.message_types_by_name['CDNSnapshotHistory']
_PAGINATION = DESCRIPTOR.message_types_by_name['Pagination']
_CDNSNAPSHOTHISTORYINFO = DESCRIPTOR.message_types_by_name['CDNSnapshotHistoryInfo']
CDNSnapshotHistory = _reflection.GeneratedProtocolMessageType('CDNSnapshotHistory', (_message.Message,), {
  'DESCRIPTOR' : _CDNSNAPSHOTHISTORY,
  '__module__' : 'live.business.snapshot_manage_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Business.CDNSnapshotHistory)
  })
_sym_db.RegisterMessage(CDNSnapshotHistory)

Pagination = _reflection.GeneratedProtocolMessageType('Pagination', (_message.Message,), {
  'DESCRIPTOR' : _PAGINATION,
  '__module__' : 'live.business.snapshot_manage_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Business.Pagination)
  })
_sym_db.RegisterMessage(Pagination)

CDNSnapshotHistoryInfo = _reflection.GeneratedProtocolMessageType('CDNSnapshotHistoryInfo', (_message.Message,), {
  'DESCRIPTOR' : _CDNSNAPSHOTHISTORYINFO,
  '__module__' : 'live.business.snapshot_manage_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Business.CDNSnapshotHistoryInfo)
  })
_sym_db.RegisterMessage(CDNSnapshotHistoryInfo)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n*com.volcengine.service.live.model.businessB\016SnapshotManageP\001ZBgithub.com/volcengine/volc-sdk-golang/service/live/models/business\240\001\001\330\001\001\302\002\000\312\002!Volc\\Service\\Live\\Models\\Business\342\002$Volc\\Service\\Live\\Models\\GPBMetadata'
  _CDNSNAPSHOTHISTORY._serialized_start=73
  _CDNSNAPSHOTHISTORY._serialized_end=251
  _PAGINATION._serialized_start=253
  _PAGINATION._serialized_end=339
  _CDNSNAPSHOTHISTORYINFO._serialized_start=342
  _CDNSNAPSHOTHISTORYINFO._serialized_end=498
# @@protoc_insertion_point(module_scope)
