# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: imp/request/request_imp.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()


from volcengine.imp.models.business import imp_common_pb2 as imp_dot_business_dot_imp__common__pb2


DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1dimp/request/request_imp.proto\x12\x1dVolcengine.Imp.Models.Request\x1a\x1dimp/business/imp_common.proto\"\xe2\x03\n\x13ImpSubmitJobRequest\x12<\n\tInputPath\x18\x01 \x01(\x0b\x32).Volcengine.Imp.Models.Business.InputPath\x12\x12\n\nTemplateId\x18\x02 \x01(\t\x12\x14\n\x0c\x43\x61llbackArgs\x18\x03 \x01(\t\x12\x19\n\x11\x45nableLowPriority\x18\x04 \x01(\t\x12\x36\n\x06Params\x18\x05 \x01(\x0b\x32&.Volcengine.Imp.Models.Business.Params\x12>\n\nOutputPath\x18\x06 \x01(\x0b\x32*.Volcengine.Imp.Models.Business.OutputPath\x12\x30\n\x03Job\x18\x07 \x01(\x0b\x32#.Volcengine.Imp.Models.Business.Job\x12\x18\n\x0b\x43\x61llbackUri\x18\x08 \x01(\tH\x00\x88\x01\x01\x12 \n\x13\x43\x61llbackContentType\x18\t \x01(\tH\x01\x88\x01\x01\x12:\n\x0bMultiInputs\x18\n \x03(\x0b\x32%.Volcengine.Imp.Models.Business.InputB\x0e\n\x0c_CallbackUriB\x16\n\x14_CallbackContentType\"\"\n\x11ImpKillJobRequest\x12\r\n\x05JobId\x18\x01 \x01(\t\"\'\n\x15ImpRetrieveJobRequest\x12\x0e\n\x06JobIds\x18\x01 \x03(\tB\xc8\x01\n(com.volcengine.service.imp.model.requestB\nImpRequestP\<EMAIL>/volcengine/volc-sdk-golang/service/imp/models/request\xa0\x01\x01\xd8\x01\x01\xca\x02\x1fVolc\\Service\\Imp\\Models\\Request\xe2\x02#Volc\\Service\\Imp\\Models\\GPBMetadatab\x06proto3')



_IMPSUBMITJOBREQUEST = DESCRIPTOR.message_types_by_name['ImpSubmitJobRequest']
_IMPKILLJOBREQUEST = DESCRIPTOR.message_types_by_name['ImpKillJobRequest']
_IMPRETRIEVEJOBREQUEST = DESCRIPTOR.message_types_by_name['ImpRetrieveJobRequest']
ImpSubmitJobRequest = _reflection.GeneratedProtocolMessageType('ImpSubmitJobRequest', (_message.Message,), {
  'DESCRIPTOR' : _IMPSUBMITJOBREQUEST,
  '__module__' : 'imp.request.request_imp_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Imp.Models.Request.ImpSubmitJobRequest)
  })
_sym_db.RegisterMessage(ImpSubmitJobRequest)

ImpKillJobRequest = _reflection.GeneratedProtocolMessageType('ImpKillJobRequest', (_message.Message,), {
  'DESCRIPTOR' : _IMPKILLJOBREQUEST,
  '__module__' : 'imp.request.request_imp_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Imp.Models.Request.ImpKillJobRequest)
  })
_sym_db.RegisterMessage(ImpKillJobRequest)

ImpRetrieveJobRequest = _reflection.GeneratedProtocolMessageType('ImpRetrieveJobRequest', (_message.Message,), {
  'DESCRIPTOR' : _IMPRETRIEVEJOBREQUEST,
  '__module__' : 'imp.request.request_imp_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Imp.Models.Request.ImpRetrieveJobRequest)
  })
_sym_db.RegisterMessage(ImpRetrieveJobRequest)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n(com.volcengine.service.imp.model.requestB\nImpRequestP\<EMAIL>/volcengine/volc-sdk-golang/service/imp/models/request\240\001\001\330\001\001\312\002\037Volc\\Service\\Imp\\Models\\Request\342\002#Volc\\Service\\Imp\\Models\\GPBMetadata'
  _IMPSUBMITJOBREQUEST._serialized_start=96
  _IMPSUBMITJOBREQUEST._serialized_end=578
  _IMPKILLJOBREQUEST._serialized_start=580
  _IMPKILLJOBREQUEST._serialized_end=614
  _IMPRETRIEVEJOBREQUEST._serialized_start=616
  _IMPRETRIEVEJOBREQUEST._serialized_end=655
# @@protoc_insertion_point(module_scope)
