# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: live/business/stream_manage.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n!live/business/stream_manage.proto\x12\x1fVolcengine.Live.Models.Business\"\xd1\x01\n\x0eStreamInfoList\x12\n\n\x02ID\x18\x01 \x01(\x03\x12\r\n\x05Vhost\x18\x02 \x01(\t\x12\x0e\n\x06\x44omain\x18\x03 \x01(\t\x12\x0b\n\x03\x41pp\x18\x04 \x01(\t\x12\x0e\n\x06Stream\x18\x05 \x01(\t\x12\x18\n\x10SessionStartTime\x18\x06 \x01(\t\x12\x12\n\nOnlineUser\x18\x07 \x01(\x03\x12\x11\n\tBandWidth\x18\x08 \x01(\x03\x12\x0f\n\x07\x42itrate\x18\t \x01(\x03\x12\x11\n\tFramerate\x18\n \x01(\x03\x12\x12\n\nPreviewURL\x18\x0b \x01(\t\"\x82\x01\n\x0c\x43losedStream\x12\r\n\x05Vhost\x18\x01 \x01(\t\x12\x0e\n\x06\x44omain\x18\x02 \x01(\t\x12\x0b\n\x03\x41pp\x18\x03 \x01(\t\x12\x0e\n\x06Stream\x18\x04 \x01(\t\x12\x11\n\tStartTime\x18\x05 \x01(\t\x12\x0f\n\x07\x45ndTime\x18\x06 \x01(\t\x12\x12\n\nSourceType\x18\x07 \x01(\t\"\x88\x01\n\x17\x46orbiddenStreamInfoList\x12\r\n\x05Vhost\x18\x01 \x01(\t\x12\x0e\n\x06\x44omain\x18\x02 \x01(\t\x12\x0b\n\x03\x41pp\x18\x03 \x01(\t\x12\x0e\n\x06Stream\x18\x04 \x01(\t\x12\x12\n\nCreateTime\x18\x05 \x01(\t\x12\x0f\n\x07\x45ndTime\x18\x06 \x01(\t\x12\x0c\n\x04type\x18\x07 \x01(\t\"m\n\x0eLiveStreamInfo\x12G\n\x0eStreamInfoList\x18\x01 \x03(\x0b\x32/.Volcengine.Live.Models.Business.StreamInfoList\x12\x12\n\nRoughCount\x18\x02 \x01(\x03\"m\n\x10\x43losedStreamInfo\x12\x45\n\x0eStreamInfoList\x18\x01 \x03(\x0b\x32-.Volcengine.Live.Models.Business.ClosedStream\x12\x12\n\nRoughCount\x18\x02 \x01(\x03\"5\n\x0fStreamStateInfo\x12\x14\n\x0cstream_state\x18\x01 \x01(\t\x12\x0c\n\x04type\x18\x02 \x01(\t\"{\n\x13\x46orbiddenStreamInfo\x12P\n\x0eStreamInfoList\x18\x01 \x03(\x0b\x32\x38.Volcengine.Live.Models.Business.ForbiddenStreamInfoList\x12\x12\n\nRoughCount\x18\x02 \x01(\x03\x42\xd4\x01\n*com.volcengine.service.live.model.businessB\x0cStreamManageP\x01ZBgithub.com/volcengine/volc-sdk-golang/service/live/models/business\xa0\x01\x01\xd8\x01\x01\xc2\x02\x00\xca\x02!Volc\\Service\\Live\\Models\\Business\xe2\x02$Volc\\Service\\Live\\Models\\GPBMetadatab\x06proto3')



_STREAMINFOLIST = DESCRIPTOR.message_types_by_name['StreamInfoList']
_CLOSEDSTREAM = DESCRIPTOR.message_types_by_name['ClosedStream']
_FORBIDDENSTREAMINFOLIST = DESCRIPTOR.message_types_by_name['ForbiddenStreamInfoList']
_LIVESTREAMINFO = DESCRIPTOR.message_types_by_name['LiveStreamInfo']
_CLOSEDSTREAMINFO = DESCRIPTOR.message_types_by_name['ClosedStreamInfo']
_STREAMSTATEINFO = DESCRIPTOR.message_types_by_name['StreamStateInfo']
_FORBIDDENSTREAMINFO = DESCRIPTOR.message_types_by_name['ForbiddenStreamInfo']
StreamInfoList = _reflection.GeneratedProtocolMessageType('StreamInfoList', (_message.Message,), {
  'DESCRIPTOR' : _STREAMINFOLIST,
  '__module__' : 'live.business.stream_manage_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Business.StreamInfoList)
  })
_sym_db.RegisterMessage(StreamInfoList)

ClosedStream = _reflection.GeneratedProtocolMessageType('ClosedStream', (_message.Message,), {
  'DESCRIPTOR' : _CLOSEDSTREAM,
  '__module__' : 'live.business.stream_manage_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Business.ClosedStream)
  })
_sym_db.RegisterMessage(ClosedStream)

ForbiddenStreamInfoList = _reflection.GeneratedProtocolMessageType('ForbiddenStreamInfoList', (_message.Message,), {
  'DESCRIPTOR' : _FORBIDDENSTREAMINFOLIST,
  '__module__' : 'live.business.stream_manage_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Business.ForbiddenStreamInfoList)
  })
_sym_db.RegisterMessage(ForbiddenStreamInfoList)

LiveStreamInfo = _reflection.GeneratedProtocolMessageType('LiveStreamInfo', (_message.Message,), {
  'DESCRIPTOR' : _LIVESTREAMINFO,
  '__module__' : 'live.business.stream_manage_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Business.LiveStreamInfo)
  })
_sym_db.RegisterMessage(LiveStreamInfo)

ClosedStreamInfo = _reflection.GeneratedProtocolMessageType('ClosedStreamInfo', (_message.Message,), {
  'DESCRIPTOR' : _CLOSEDSTREAMINFO,
  '__module__' : 'live.business.stream_manage_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Business.ClosedStreamInfo)
  })
_sym_db.RegisterMessage(ClosedStreamInfo)

StreamStateInfo = _reflection.GeneratedProtocolMessageType('StreamStateInfo', (_message.Message,), {
  'DESCRIPTOR' : _STREAMSTATEINFO,
  '__module__' : 'live.business.stream_manage_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Business.StreamStateInfo)
  })
_sym_db.RegisterMessage(StreamStateInfo)

ForbiddenStreamInfo = _reflection.GeneratedProtocolMessageType('ForbiddenStreamInfo', (_message.Message,), {
  'DESCRIPTOR' : _FORBIDDENSTREAMINFO,
  '__module__' : 'live.business.stream_manage_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Business.ForbiddenStreamInfo)
  })
_sym_db.RegisterMessage(ForbiddenStreamInfo)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n*com.volcengine.service.live.model.businessB\014StreamManageP\001ZBgithub.com/volcengine/volc-sdk-golang/service/live/models/business\240\001\001\330\001\001\302\002\000\312\002!Volc\\Service\\Live\\Models\\Business\342\002$Volc\\Service\\Live\\Models\\GPBMetadata'
  _STREAMINFOLIST._serialized_start=71
  _STREAMINFOLIST._serialized_end=280
  _CLOSEDSTREAM._serialized_start=283
  _CLOSEDSTREAM._serialized_end=413
  _FORBIDDENSTREAMINFOLIST._serialized_start=416
  _FORBIDDENSTREAMINFOLIST._serialized_end=552
  _LIVESTREAMINFO._serialized_start=554
  _LIVESTREAMINFO._serialized_end=663
  _CLOSEDSTREAMINFO._serialized_start=665
  _CLOSEDSTREAMINFO._serialized_end=774
  _STREAMSTATEINFO._serialized_start=776
  _STREAMSTATEINFO._serialized_end=829
  _FORBIDDENSTREAMINFO._serialized_start=831
  _FORBIDDENSTREAMINFO._serialized_end=954
# @@protoc_insertion_point(module_scope)
