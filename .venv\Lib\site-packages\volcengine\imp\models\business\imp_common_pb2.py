# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: imp/business/imp_common.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1dimp/business/imp_common.proto\x12\x1eVolcengine.Imp.Models.Business\"R\n\tInputPath\x12\x0c\n\x04Type\x18\x01 \x01(\t\x12\x11\n\tTosBucket\x18\x02 \x01(\t\x12\x14\n\x0cVodSpaceName\x18\x03 \x01(\t\x12\x0e\n\x06\x46ileId\x18\x04 \x01(\t\"y\n\x05Input\x12<\n\tInputPath\x18\x01 \x01(\x0b\x32).Volcengine.Imp.Models.Business.InputPath\x12\x32\n\x04\x43lip\x18\x02 \x01(\x0b\x32$.Volcengine.Imp.Models.Business.Clip\"*\n\x04\x43lip\x12\x11\n\tStartTime\x18\x01 \x01(\x05\x12\x0f\n\x07\x45ndTime\x18\x02 \x01(\x05\"\xc8\x01\n\tJobOutput\x12\x12\n\nTemplateId\x18\x01 \x01(\t\x12\x12\n\nProperties\x18\x02 \x01(\t\x12\x0c\n\x04\x43ode\x18\x03 \x01(\t\x12\x15\n\rFileMessageId\x18\x04 \x01(\t\x12\x10\n\x08TaskType\x18\x05 \x01(\t\x12\x0e\n\x06Status\x18\x06 \x01(\t\x12\x12\n\nActivityId\x18\x07 \x01(\t\x12\x11\n\tStartTime\x18\x08 \x01(\t\x12\x0f\n\x07\x45ndTime\x18\t \x01(\t\x12\x14\n\x0cTemplateName\x18\n \x01(\t\"\xaf\x03\n\x0cJobExecution\x12\r\n\x05JobId\x18\x01 \x01(\t\x12<\n\tInputPath\x18\x02 \x01(\x0b\x32).Volcengine.Imp.Models.Business.InputPath\x12\x39\n\x06Output\x18\x03 \x03(\x0b\x32).Volcengine.Imp.Models.Business.JobOutput\x12\x0e\n\x06Status\x18\x04 \x01(\t\x12\x11\n\tCreatedAt\x18\x05 \x01(\t\x12\x12\n\nFinishedAt\x18\x06 \x01(\t\x12\x12\n\nTemplateId\x18\x07 \x01(\t\x12\x19\n\x11\x45nableLowPriority\x18\x08 \x01(\t\x12\x11\n\tJobSource\x18\t \x01(\t\x12\x30\n\x03Job\x18\n \x01(\x0b\x32#.Volcengine.Imp.Models.Business.Job\x12\x13\n\x0b\x43\x61llbackUri\x18\x0b \x01(\t\x12\x1b\n\x13\x43\x61llbackContentType\x18\x0c \x01(\t\x12:\n\x0bMultiInputs\x18\r \x03(\x0b\x32%.Volcengine.Imp.Models.Business.Input\"P\n\x06Params\x12\x46\n\x0eOverrideParams\x18\x01 \x01(\x0b\x32..Volcengine.Imp.Models.Business.OverrideParams\"\xf8\x01\n\x0eOverrideParams\x12L\n\nSmartErase\x18\x01 \x03(\x0b\x32\x38.Volcengine.Imp.Models.Business.SmartEraseOverrideParams\x12\x44\n\x06Output\x18\x02 \x03(\x0b\x32\x34.Volcengine.Imp.Models.Business.OutputOverrideParams\x12R\n\rSmartEmoticon\x18\x03 \x03(\x0b\x32;.Volcengine.Imp.Models.Business.SmartEmoticonOverrideParams\"\x9e\x01\n\x18SmartEraseOverrideParams\x12\x12\n\nActivityId\x18\x01 \x03(\t\x12<\n\tWatermark\x18\x02 \x01(\x0b\x32).Volcengine.Imp.Models.Business.Watermark\x12\x30\n\x03OCR\x18\x03 \x01(\x0b\x32#.Volcengine.Imp.Models.Business.OCR\"K\n\tWatermark\x12>\n\nDetectRect\x18\x01 \x03(\x0b\x32*.Volcengine.Imp.Models.Business.DetectRect\"E\n\x03OCR\x12>\n\nDetectRect\x18\x01 \x03(\x0b\x32*.Volcengine.Imp.Models.Business.DetectRect\"<\n\nDetectRect\x12\n\n\x02X1\x18\x01 \x01(\x01\x12\n\n\x02Y1\x18\x02 \x01(\x01\x12\n\n\x02X2\x18\x03 \x01(\x01\x12\n\n\x02Y2\x18\x04 \x01(\x01\"j\n\x14OutputOverrideParams\x12\x12\n\nActivityId\x18\x01 \x03(\t\x12>\n\nOutputPath\x18\x02 \x01(\x0b\x32*.Volcengine.Imp.Models.Business.OutputPath\"\xfd\x01\n\x1bSmartEmoticonOverrideParams\x12\x12\n\nActivityId\x18\x01 \x03(\t\x12>\n\x0b\x44rivenVideo\x18\x02 \x01(\x0b\x32).Volcengine.Imp.Models.Business.InputPath\x12>\n\x0b\x44rivenAudio\x18\x03 \x01(\x0b\x32).Volcengine.Imp.Models.Business.InputPath\x12J\n\x10\x44rivenTextParams\x18\x04 \x01(\x0b\x32\x30.Volcengine.Imp.Models.Business.DrivenTextParams\"d\n\x10\x44rivenTextParams\x12\x12\n\nDrivenText\x18\x01 \x01(\t\x12<\n\tTTSParams\x18\x02 \x01(\x0b\x32).Volcengine.Imp.Models.Business.TTSParams\"\x1e\n\tTTSParams\x12\x11\n\tVoiceType\x18\x01 \x01(\t\"U\n\nOutputPath\x12\x0c\n\x04Type\x18\x01 \x01(\t\x12\x11\n\tTosBucket\x18\x02 \x01(\t\x12\x14\n\x0cVodSpaceName\x18\x03 \x01(\t\x12\x10\n\x08\x46ileName\x18\x04 \x01(\t\"\x8b\x01\n\x03Job\x12I\n\x0eTranscodeVideo\x18\x01 \x01(\x0b\x32\x31.Volcengine.Imp.Models.Business.TranscodeVideoJob\x12\x39\n\x06\x42yteHD\x18\x02 \x01(\x0b\x32).Volcengine.Imp.Models.Business.ByteHDJob\"\xcc\x02\n\x11TranscodeVideoJob\x12\x11\n\tContainer\x18\x01 \x01(\t\x12\x34\n\x05Video\x18\x02 \x01(\x0b\x32%.Volcengine.Imp.Models.Business.Video\x12\x34\n\x05\x41udio\x18\x03 \x01(\x0b\x32%.Volcengine.Imp.Models.Business.Audio\x12\x13\n\x0b\x45nableRemux\x18\x04 \x01(\x08\x12\x14\n\x0c\x44isableVideo\x18\x05 \x01(\x08\x12\x14\n\x0c\x44isableAudio\x18\x06 \x01(\x08\x12\x38\n\x07Segment\x18\x07 \x01(\x0b\x32\'.Volcengine.Imp.Models.Business.Segment\x12=\n\x05Logos\x18\x08 \x03(\x0b\x32..Volcengine.Imp.Models.Business.LogoDefinition\"\x83\x02\n\tByteHDJob\x12\x11\n\tContainer\x18\x01 \x01(\t\x12\x34\n\x05Video\x18\x02 \x01(\x0b\x32%.Volcengine.Imp.Models.Business.Video\x12\x34\n\x05\x41udio\x18\x03 \x01(\x0b\x32%.Volcengine.Imp.Models.Business.Audio\x12\x38\n\x07Segment\x18\x04 \x01(\x0b\x32\'.Volcengine.Imp.Models.Business.Segment\x12=\n\x05Logos\x18\x05 \x03(\x0b\x32..Volcengine.Imp.Models.Business.LogoDefinition\"\xd5\x01\n\x05Video\x12\r\n\x05\x43odec\x18\x01 \x01(\t\x12\x11\n\tScaleType\x18\x02 \x01(\x05\x12\x12\n\nScaleWidth\x18\x03 \x01(\x05\x12\x13\n\x0bScaleHeight\x18\x04 \x01(\x05\x12\x12\n\nScaleShort\x18\x05 \x01(\x05\x12\x11\n\tScaleLong\x18\x06 \x01(\x05\x12\x0f\n\x07\x42itrate\x18\x07 \x01(\x05\x12\x0e\n\x06MaxFps\x18\x08 \x01(\x05\x12\x10\n\x03\x43rf\x18\t \x01(\x05H\x00\x88\x01\x01\x12\x0f\n\x07Profile\x18\n \x01(\t\x12\x0e\n\x06PixFmt\x18\x0b \x01(\tB\x06\n\x04_Crf\"\x9c\x01\n\x06Volume\x12\x0e\n\x06Method\x18\x01 \x01(\t\x12\x1f\n\x12IntegratedLoudness\x18\x02 \x01(\x01H\x00\x88\x01\x01\x12\x15\n\x08TruePeak\x18\x03 \x01(\x01H\x01\x88\x01\x01\x12\x17\n\nVolumeTime\x18\x04 \x01(\x01H\x02\x88\x01\x01\x42\x15\n\x13_IntegratedLoudnessB\x0b\n\t_TruePeakB\r\n\x0b_VolumeTime\"\x96\x01\n\x05\x41udio\x12\r\n\x05\x43odec\x18\x01 \x01(\t\x12\x0f\n\x07Profile\x18\x02 \x01(\t\x12\x12\n\nSampleRate\x18\x03 \x01(\x05\x12\x0f\n\x07\x42itrate\x18\x04 \x01(\x05\x12\x10\n\x08\x43hannels\x18\x05 \x01(\x05\x12\x36\n\x06Volume\x18\x06 \x01(\x0b\x32&.Volcengine.Imp.Models.Business.Volume\"9\n\x07Segment\x12\x0e\n\x06\x46ormat\x18\x01 \x01(\t\x12\x0c\n\x04Type\x18\x02 \x01(\t\x12\x10\n\x08\x44uration\x18\x03 \x01(\x05\"\xbc\x02\n\x0eLogoDefinition\x12\x0c\n\x04Type\x18\x01 \x01(\t\x12N\n\x12TextLogoDefinition\x18\x02 \x01(\x0b\x32\x32.Volcengine.Imp.Models.Business.TextLogoDefinition\x12P\n\x13ImageLogoDefinition\x18\x03 \x01(\x0b\x32\x33.Volcengine.Imp.Models.Business.ImageLogoDefinition\x12>\n\x08Position\x18\x04 \x01(\x0b\x32,.Volcengine.Imp.Models.Business.LogoPosition\x12:\n\x08TimeLine\x18\x05 \x01(\x0b\x32(.Volcengine.Imp.Models.Business.TimeLine\"\\\n\x12TextLogoDefinition\x12\x0f\n\x07\x43ontent\x18\x01 \x01(\t\x12\x10\n\x08\x46ontType\x18\x02 \x01(\t\x12\x10\n\x08\x46ontSize\x18\x03 \x01(\x05\x12\x11\n\tFontColor\x18\x04 \x01(\t\"\x8e\x01\n\x13ImageLogoDefinition\x12:\n\x07\x43ontent\x18\x01 \x01(\x0b\x32).Volcengine.Imp.Models.Business.InputPath\x12\x11\n\tLoopTimes\x18\x02 \x01(\x05\x12\x12\n\nRepeatLast\x18\x03 \x01(\x08\x12\x14\n\x0cTransparency\x18\x04 \x01(\x05\"X\n\x0cLogoPosition\x12\x0c\n\x04PosX\x18\x01 \x01(\t\x12\x0c\n\x04PosY\x18\x02 \x01(\t\x12\r\n\x05SizeX\x18\x04 \x01(\t\x12\r\n\x05SizeY\x18\x05 \x01(\t\x12\x0e\n\x06Locate\x18\x06 \x01(\t\".\n\x08TimeLine\x12\x11\n\tStartTime\x18\x01 \x01(\x05\x12\x0f\n\x07\x45ndTime\x18\x02 \x01(\x05\x42\xcc\x01\n)com.volcengine.service.imp.model.businessB\x0bImpWorkflowP\x01ZAgithub.com/volcengine/volc-sdk-golang/service/imp/models/business\xa0\x01\x01\xd8\x01\x01\xca\x02 Volc\\Service\\Imp\\Models\\Business\xe2\x02#Volc\\Service\\Imp\\Models\\GPBMetadatab\x06proto3')



_INPUTPATH = DESCRIPTOR.message_types_by_name['InputPath']
_INPUT = DESCRIPTOR.message_types_by_name['Input']
_CLIP = DESCRIPTOR.message_types_by_name['Clip']
_JOBOUTPUT = DESCRIPTOR.message_types_by_name['JobOutput']
_JOBEXECUTION = DESCRIPTOR.message_types_by_name['JobExecution']
_PARAMS = DESCRIPTOR.message_types_by_name['Params']
_OVERRIDEPARAMS = DESCRIPTOR.message_types_by_name['OverrideParams']
_SMARTERASEOVERRIDEPARAMS = DESCRIPTOR.message_types_by_name['SmartEraseOverrideParams']
_WATERMARK = DESCRIPTOR.message_types_by_name['Watermark']
_OCR = DESCRIPTOR.message_types_by_name['OCR']
_DETECTRECT = DESCRIPTOR.message_types_by_name['DetectRect']
_OUTPUTOVERRIDEPARAMS = DESCRIPTOR.message_types_by_name['OutputOverrideParams']
_SMARTEMOTICONOVERRIDEPARAMS = DESCRIPTOR.message_types_by_name['SmartEmoticonOverrideParams']
_DRIVENTEXTPARAMS = DESCRIPTOR.message_types_by_name['DrivenTextParams']
_TTSPARAMS = DESCRIPTOR.message_types_by_name['TTSParams']
_OUTPUTPATH = DESCRIPTOR.message_types_by_name['OutputPath']
_JOB = DESCRIPTOR.message_types_by_name['Job']
_TRANSCODEVIDEOJOB = DESCRIPTOR.message_types_by_name['TranscodeVideoJob']
_BYTEHDJOB = DESCRIPTOR.message_types_by_name['ByteHDJob']
_VIDEO = DESCRIPTOR.message_types_by_name['Video']
_VOLUME = DESCRIPTOR.message_types_by_name['Volume']
_AUDIO = DESCRIPTOR.message_types_by_name['Audio']
_SEGMENT = DESCRIPTOR.message_types_by_name['Segment']
_LOGODEFINITION = DESCRIPTOR.message_types_by_name['LogoDefinition']
_TEXTLOGODEFINITION = DESCRIPTOR.message_types_by_name['TextLogoDefinition']
_IMAGELOGODEFINITION = DESCRIPTOR.message_types_by_name['ImageLogoDefinition']
_LOGOPOSITION = DESCRIPTOR.message_types_by_name['LogoPosition']
_TIMELINE = DESCRIPTOR.message_types_by_name['TimeLine']
InputPath = _reflection.GeneratedProtocolMessageType('InputPath', (_message.Message,), {
  'DESCRIPTOR' : _INPUTPATH,
  '__module__' : 'imp.business.imp_common_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Imp.Models.Business.InputPath)
  })
_sym_db.RegisterMessage(InputPath)

Input = _reflection.GeneratedProtocolMessageType('Input', (_message.Message,), {
  'DESCRIPTOR' : _INPUT,
  '__module__' : 'imp.business.imp_common_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Imp.Models.Business.Input)
  })
_sym_db.RegisterMessage(Input)

Clip = _reflection.GeneratedProtocolMessageType('Clip', (_message.Message,), {
  'DESCRIPTOR' : _CLIP,
  '__module__' : 'imp.business.imp_common_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Imp.Models.Business.Clip)
  })
_sym_db.RegisterMessage(Clip)

JobOutput = _reflection.GeneratedProtocolMessageType('JobOutput', (_message.Message,), {
  'DESCRIPTOR' : _JOBOUTPUT,
  '__module__' : 'imp.business.imp_common_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Imp.Models.Business.JobOutput)
  })
_sym_db.RegisterMessage(JobOutput)

JobExecution = _reflection.GeneratedProtocolMessageType('JobExecution', (_message.Message,), {
  'DESCRIPTOR' : _JOBEXECUTION,
  '__module__' : 'imp.business.imp_common_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Imp.Models.Business.JobExecution)
  })
_sym_db.RegisterMessage(JobExecution)

Params = _reflection.GeneratedProtocolMessageType('Params', (_message.Message,), {
  'DESCRIPTOR' : _PARAMS,
  '__module__' : 'imp.business.imp_common_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Imp.Models.Business.Params)
  })
_sym_db.RegisterMessage(Params)

OverrideParams = _reflection.GeneratedProtocolMessageType('OverrideParams', (_message.Message,), {
  'DESCRIPTOR' : _OVERRIDEPARAMS,
  '__module__' : 'imp.business.imp_common_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Imp.Models.Business.OverrideParams)
  })
_sym_db.RegisterMessage(OverrideParams)

SmartEraseOverrideParams = _reflection.GeneratedProtocolMessageType('SmartEraseOverrideParams', (_message.Message,), {
  'DESCRIPTOR' : _SMARTERASEOVERRIDEPARAMS,
  '__module__' : 'imp.business.imp_common_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Imp.Models.Business.SmartEraseOverrideParams)
  })
_sym_db.RegisterMessage(SmartEraseOverrideParams)

Watermark = _reflection.GeneratedProtocolMessageType('Watermark', (_message.Message,), {
  'DESCRIPTOR' : _WATERMARK,
  '__module__' : 'imp.business.imp_common_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Imp.Models.Business.Watermark)
  })
_sym_db.RegisterMessage(Watermark)

OCR = _reflection.GeneratedProtocolMessageType('OCR', (_message.Message,), {
  'DESCRIPTOR' : _OCR,
  '__module__' : 'imp.business.imp_common_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Imp.Models.Business.OCR)
  })
_sym_db.RegisterMessage(OCR)

DetectRect = _reflection.GeneratedProtocolMessageType('DetectRect', (_message.Message,), {
  'DESCRIPTOR' : _DETECTRECT,
  '__module__' : 'imp.business.imp_common_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Imp.Models.Business.DetectRect)
  })
_sym_db.RegisterMessage(DetectRect)

OutputOverrideParams = _reflection.GeneratedProtocolMessageType('OutputOverrideParams', (_message.Message,), {
  'DESCRIPTOR' : _OUTPUTOVERRIDEPARAMS,
  '__module__' : 'imp.business.imp_common_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Imp.Models.Business.OutputOverrideParams)
  })
_sym_db.RegisterMessage(OutputOverrideParams)

SmartEmoticonOverrideParams = _reflection.GeneratedProtocolMessageType('SmartEmoticonOverrideParams', (_message.Message,), {
  'DESCRIPTOR' : _SMARTEMOTICONOVERRIDEPARAMS,
  '__module__' : 'imp.business.imp_common_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Imp.Models.Business.SmartEmoticonOverrideParams)
  })
_sym_db.RegisterMessage(SmartEmoticonOverrideParams)

DrivenTextParams = _reflection.GeneratedProtocolMessageType('DrivenTextParams', (_message.Message,), {
  'DESCRIPTOR' : _DRIVENTEXTPARAMS,
  '__module__' : 'imp.business.imp_common_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Imp.Models.Business.DrivenTextParams)
  })
_sym_db.RegisterMessage(DrivenTextParams)

TTSParams = _reflection.GeneratedProtocolMessageType('TTSParams', (_message.Message,), {
  'DESCRIPTOR' : _TTSPARAMS,
  '__module__' : 'imp.business.imp_common_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Imp.Models.Business.TTSParams)
  })
_sym_db.RegisterMessage(TTSParams)

OutputPath = _reflection.GeneratedProtocolMessageType('OutputPath', (_message.Message,), {
  'DESCRIPTOR' : _OUTPUTPATH,
  '__module__' : 'imp.business.imp_common_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Imp.Models.Business.OutputPath)
  })
_sym_db.RegisterMessage(OutputPath)

Job = _reflection.GeneratedProtocolMessageType('Job', (_message.Message,), {
  'DESCRIPTOR' : _JOB,
  '__module__' : 'imp.business.imp_common_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Imp.Models.Business.Job)
  })
_sym_db.RegisterMessage(Job)

TranscodeVideoJob = _reflection.GeneratedProtocolMessageType('TranscodeVideoJob', (_message.Message,), {
  'DESCRIPTOR' : _TRANSCODEVIDEOJOB,
  '__module__' : 'imp.business.imp_common_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Imp.Models.Business.TranscodeVideoJob)
  })
_sym_db.RegisterMessage(TranscodeVideoJob)

ByteHDJob = _reflection.GeneratedProtocolMessageType('ByteHDJob', (_message.Message,), {
  'DESCRIPTOR' : _BYTEHDJOB,
  '__module__' : 'imp.business.imp_common_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Imp.Models.Business.ByteHDJob)
  })
_sym_db.RegisterMessage(ByteHDJob)

Video = _reflection.GeneratedProtocolMessageType('Video', (_message.Message,), {
  'DESCRIPTOR' : _VIDEO,
  '__module__' : 'imp.business.imp_common_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Imp.Models.Business.Video)
  })
_sym_db.RegisterMessage(Video)

Volume = _reflection.GeneratedProtocolMessageType('Volume', (_message.Message,), {
  'DESCRIPTOR' : _VOLUME,
  '__module__' : 'imp.business.imp_common_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Imp.Models.Business.Volume)
  })
_sym_db.RegisterMessage(Volume)

Audio = _reflection.GeneratedProtocolMessageType('Audio', (_message.Message,), {
  'DESCRIPTOR' : _AUDIO,
  '__module__' : 'imp.business.imp_common_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Imp.Models.Business.Audio)
  })
_sym_db.RegisterMessage(Audio)

Segment = _reflection.GeneratedProtocolMessageType('Segment', (_message.Message,), {
  'DESCRIPTOR' : _SEGMENT,
  '__module__' : 'imp.business.imp_common_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Imp.Models.Business.Segment)
  })
_sym_db.RegisterMessage(Segment)

LogoDefinition = _reflection.GeneratedProtocolMessageType('LogoDefinition', (_message.Message,), {
  'DESCRIPTOR' : _LOGODEFINITION,
  '__module__' : 'imp.business.imp_common_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Imp.Models.Business.LogoDefinition)
  })
_sym_db.RegisterMessage(LogoDefinition)

TextLogoDefinition = _reflection.GeneratedProtocolMessageType('TextLogoDefinition', (_message.Message,), {
  'DESCRIPTOR' : _TEXTLOGODEFINITION,
  '__module__' : 'imp.business.imp_common_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Imp.Models.Business.TextLogoDefinition)
  })
_sym_db.RegisterMessage(TextLogoDefinition)

ImageLogoDefinition = _reflection.GeneratedProtocolMessageType('ImageLogoDefinition', (_message.Message,), {
  'DESCRIPTOR' : _IMAGELOGODEFINITION,
  '__module__' : 'imp.business.imp_common_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Imp.Models.Business.ImageLogoDefinition)
  })
_sym_db.RegisterMessage(ImageLogoDefinition)

LogoPosition = _reflection.GeneratedProtocolMessageType('LogoPosition', (_message.Message,), {
  'DESCRIPTOR' : _LOGOPOSITION,
  '__module__' : 'imp.business.imp_common_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Imp.Models.Business.LogoPosition)
  })
_sym_db.RegisterMessage(LogoPosition)

TimeLine = _reflection.GeneratedProtocolMessageType('TimeLine', (_message.Message,), {
  'DESCRIPTOR' : _TIMELINE,
  '__module__' : 'imp.business.imp_common_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Imp.Models.Business.TimeLine)
  })
_sym_db.RegisterMessage(TimeLine)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n)com.volcengine.service.imp.model.businessB\013ImpWorkflowP\001ZAgithub.com/volcengine/volc-sdk-golang/service/imp/models/business\240\001\001\330\001\001\312\002 Volc\\Service\\Imp\\Models\\Business\342\002#Volc\\Service\\Imp\\Models\\GPBMetadata'
  _INPUTPATH._serialized_start=65
  _INPUTPATH._serialized_end=147
  _INPUT._serialized_start=149
  _INPUT._serialized_end=270
  _CLIP._serialized_start=272
  _CLIP._serialized_end=314
  _JOBOUTPUT._serialized_start=317
  _JOBOUTPUT._serialized_end=517
  _JOBEXECUTION._serialized_start=520
  _JOBEXECUTION._serialized_end=951
  _PARAMS._serialized_start=953
  _PARAMS._serialized_end=1033
  _OVERRIDEPARAMS._serialized_start=1036
  _OVERRIDEPARAMS._serialized_end=1284
  _SMARTERASEOVERRIDEPARAMS._serialized_start=1287
  _SMARTERASEOVERRIDEPARAMS._serialized_end=1445
  _WATERMARK._serialized_start=1447
  _WATERMARK._serialized_end=1522
  _OCR._serialized_start=1524
  _OCR._serialized_end=1593
  _DETECTRECT._serialized_start=1595
  _DETECTRECT._serialized_end=1655
  _OUTPUTOVERRIDEPARAMS._serialized_start=1657
  _OUTPUTOVERRIDEPARAMS._serialized_end=1763
  _SMARTEMOTICONOVERRIDEPARAMS._serialized_start=1766
  _SMARTEMOTICONOVERRIDEPARAMS._serialized_end=2019
  _DRIVENTEXTPARAMS._serialized_start=2021
  _DRIVENTEXTPARAMS._serialized_end=2121
  _TTSPARAMS._serialized_start=2123
  _TTSPARAMS._serialized_end=2153
  _OUTPUTPATH._serialized_start=2155
  _OUTPUTPATH._serialized_end=2240
  _JOB._serialized_start=2243
  _JOB._serialized_end=2382
  _TRANSCODEVIDEOJOB._serialized_start=2385
  _TRANSCODEVIDEOJOB._serialized_end=2717
  _BYTEHDJOB._serialized_start=2720
  _BYTEHDJOB._serialized_end=2979
  _VIDEO._serialized_start=2982
  _VIDEO._serialized_end=3195
  _VOLUME._serialized_start=3198
  _VOLUME._serialized_end=3354
  _AUDIO._serialized_start=3357
  _AUDIO._serialized_end=3507
  _SEGMENT._serialized_start=3509
  _SEGMENT._serialized_end=3566
  _LOGODEFINITION._serialized_start=3569
  _LOGODEFINITION._serialized_end=3885
  _TEXTLOGODEFINITION._serialized_start=3887
  _TEXTLOGODEFINITION._serialized_end=3979
  _IMAGELOGODEFINITION._serialized_start=3982
  _IMAGELOGODEFINITION._serialized_end=4124
  _LOGOPOSITION._serialized_start=4126
  _LOGOPOSITION._serialized_end=4214
  _TIMELINE._serialized_start=4216
  _TIMELINE._serialized_end=4262
# @@protoc_insertion_point(module_scope)
