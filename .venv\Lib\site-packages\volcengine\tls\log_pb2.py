# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: log.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\tlog.proto\x12\x02pb\"(\n\nLogContent\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\"5\n\x03Log\x12\x0c\n\x04time\x18\x01 \x01(\x03\x12 \n\x08\x63ontents\x18\x02 \x03(\x0b\x32\x0e.pb.LogContent\"$\n\x06LogTag\x12\x0b\n\x03key\x18\x01 \x01(\t\x12\r\n\x05value\x18\x02 \x01(\t\"w\n\x08LogGroup\x12\x15\n\x04logs\x18\x01 \x03(\x0b\x32\x07.pb.Log\x12\x0e\n\x06source\x18\x02 \x01(\t\x12\x1c\n\x08log_tags\x18\x03 \x03(\x0b\x32\n.pb.LogTag\x12\x10\n\x08\x66ilename\x18\x04 \x01(\t\x12\x14\n\x0c\x63ontext_flow\x18\x05 \x01(\t\"0\n\x0cLogGroupList\x12 \n\nlog_groups\x18\x01 \x03(\x0b\x32\x0c.pb.LogGroupb\x06proto3')



_LOGCONTENT = DESCRIPTOR.message_types_by_name['LogContent']
_LOG = DESCRIPTOR.message_types_by_name['Log']
_LOGTAG = DESCRIPTOR.message_types_by_name['LogTag']
_LOGGROUP = DESCRIPTOR.message_types_by_name['LogGroup']
_LOGGROUPLIST = DESCRIPTOR.message_types_by_name['LogGroupList']
LogContent = _reflection.GeneratedProtocolMessageType('LogContent', (_message.Message,), {
  'DESCRIPTOR' : _LOGCONTENT,
  '__module__' : 'log_pb2'
  # @@protoc_insertion_point(class_scope:pb.LogContent)
  })
_sym_db.RegisterMessage(LogContent)

Log = _reflection.GeneratedProtocolMessageType('Log', (_message.Message,), {
  'DESCRIPTOR' : _LOG,
  '__module__' : 'log_pb2'
  # @@protoc_insertion_point(class_scope:pb.Log)
  })
_sym_db.RegisterMessage(Log)

LogTag = _reflection.GeneratedProtocolMessageType('LogTag', (_message.Message,), {
  'DESCRIPTOR' : _LOGTAG,
  '__module__' : 'log_pb2'
  # @@protoc_insertion_point(class_scope:pb.LogTag)
  })
_sym_db.RegisterMessage(LogTag)

LogGroup = _reflection.GeneratedProtocolMessageType('LogGroup', (_message.Message,), {
  'DESCRIPTOR' : _LOGGROUP,
  '__module__' : 'log_pb2'
  # @@protoc_insertion_point(class_scope:pb.LogGroup)
  })
_sym_db.RegisterMessage(LogGroup)

LogGroupList = _reflection.GeneratedProtocolMessageType('LogGroupList', (_message.Message,), {
  'DESCRIPTOR' : _LOGGROUPLIST,
  '__module__' : 'log_pb2'
  # @@protoc_insertion_point(class_scope:pb.LogGroupList)
  })
_sym_db.RegisterMessage(LogGroupList)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  _LOGCONTENT._serialized_start=17
  _LOGCONTENT._serialized_end=57
  _LOG._serialized_start=59
  _LOG._serialized_end=112
  _LOGTAG._serialized_start=114
  _LOGTAG._serialized_end=150
  _LOGGROUP._serialized_start=152
  _LOGGROUP._serialized_end=271
  _LOGGROUPLIST._serialized_start=273
  _LOGGROUPLIST._serialized_end=321
# @@protoc_insertion_point(module_scope)
