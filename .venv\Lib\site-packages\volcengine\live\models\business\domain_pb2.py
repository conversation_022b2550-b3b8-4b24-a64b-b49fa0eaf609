# -*- coding: utf-8 -*-
# Generated by the protocol buffer compiler.  DO NOT EDIT!
# source: live/business/domain.proto
"""Generated protocol buffer code."""
from google.protobuf import descriptor as _descriptor
from google.protobuf import descriptor_pool as _descriptor_pool
from google.protobuf import message as _message
from google.protobuf import reflection as _reflection
from google.protobuf import symbol_database as _symbol_database
# @@protoc_insertion_point(imports)

_sym_db = _symbol_database.Default()




DESCRIPTOR = _descriptor_pool.Default().AddSerializedFile(b'\n\x1alive/business/domain.proto\x12\x1fVolcengine.Live.Models.Business\"\x82\x02\n\nDomainList\x12\r\n\x05Vhost\x18\x01 \x01(\t\x12\x0e\n\x06\x44omain\x18\x02 \x01(\t\x12\x0e\n\x06Status\x18\x03 \x01(\x03\x12\x0c\n\x04Type\x18\x04 \x01(\t\x12\x0e\n\x06Region\x18\x05 \x01(\t\x12\r\n\x05\x43Name\x18\x06 \x01(\t\x12\x12\n\nCnameCheck\x18\x07 \x01(\x03\x12\x13\n\x0b\x44omainCheck\x18\x08 \x01(\x03\x12\x10\n\x08ICPCheck\x18\t \x01(\x03\x12\x12\n\nCreateTime\x18\n \x01(\t\x12\x12\n\nCertDomain\x18\x0b \x01(\t\x12\x0f\n\x07\x43hainID\x18\x0c \x01(\t\x12\x10\n\x08\x43\x65rtName\x18\r \x01(\t\x12\x12\n\nPushDomain\x18\x0e \x01(\t\"`\n\x0e\x44omainListInfo\x12?\n\nDomainList\x18\x01 \x03(\x0b\x32+.Volcengine.Live.Models.Business.DomainList\x12\r\n\x05Total\x18\x02 \x01(\x03\x42\xce\x01\n*com.volcengine.service.live.model.businessB\x06\x44omainP\x01ZBgithub.com/volcengine/volc-sdk-golang/service/live/models/business\xa0\x01\x01\xd8\x01\x01\xc2\x02\x00\xca\x02!Volc\\Service\\Live\\Models\\Business\xe2\x02$Volc\\Service\\Live\\Models\\GPBMetadatab\x06proto3')



_DOMAINLIST = DESCRIPTOR.message_types_by_name['DomainList']
_DOMAINLISTINFO = DESCRIPTOR.message_types_by_name['DomainListInfo']
DomainList = _reflection.GeneratedProtocolMessageType('DomainList', (_message.Message,), {
  'DESCRIPTOR' : _DOMAINLIST,
  '__module__' : 'live.business.domain_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Business.DomainList)
  })
_sym_db.RegisterMessage(DomainList)

DomainListInfo = _reflection.GeneratedProtocolMessageType('DomainListInfo', (_message.Message,), {
  'DESCRIPTOR' : _DOMAINLISTINFO,
  '__module__' : 'live.business.domain_pb2'
  # @@protoc_insertion_point(class_scope:Volcengine.Live.Models.Business.DomainListInfo)
  })
_sym_db.RegisterMessage(DomainListInfo)

if _descriptor._USE_C_DESCRIPTORS == False:

  DESCRIPTOR._options = None
  DESCRIPTOR._serialized_options = b'\n*com.volcengine.service.live.model.businessB\006DomainP\001ZBgithub.com/volcengine/volc-sdk-golang/service/live/models/business\240\001\001\330\001\001\302\002\000\312\002!Volc\\Service\\Live\\Models\\Business\342\002$Volc\\Service\\Live\\Models\\GPBMetadata'
  _DOMAINLIST._serialized_start=64
  _DOMAINLIST._serialized_end=322
  _DOMAINLISTINFO._serialized_start=324
  _DOMAINLISTINFO._serialized_end=420
# @@protoc_insertion_point(module_scope)
